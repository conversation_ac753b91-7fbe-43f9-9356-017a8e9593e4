'use client';

import React, { useState } from 'react';
import { observer } from 'mobx-react-lite';
import DiffStore from './stores/diffStore';
import PrimaryKeyConfig from './components/PrimaryKeyConfig';
import DiffConfirmation from './components/DiffConfirmation';
import ArgusMapping from './components/ArgusMapping';
import ResultPreview from './components/ResultPreview';

const DiffPage: React.FC = observer(() => {
  const [store] = useState(() => new DiffStore());

  const steps = [
    { title: '主键配置', component: PrimaryKeyConfig },
    { title: '差异确认', component: DiffConfirmation },
    { title: 'Argus映射', component: ArgusMapping },
    { title: '结果预览', component: ResultPreview }
  ];

  const CurrentStepComponent = steps[store.currentStep].component;

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg">
          <div className="border-b border-gray-200">
            <div className="px-6 py-4">
              <h1 className="text-2xl font-bold text-gray-900">随访报告差异对比</h1>
              <p className="text-gray-600 mt-1">比较随访报告与Argus报告的差异</p>
            </div>
            
            {/* 步骤指示器 */}
            <div className="px-6 pb-4">
              <div className="flex items-center">
                {steps.map((step, index) => (
                  <React.Fragment key={index}>
                    <div className={`flex items-center ${index <= store.currentStep ? 'text-purple-600' : 'text-gray-400'}`}>
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                        index < store.currentStep ? 'bg-purple-600 text-white' : 
                        index === store.currentStep ? 'bg-purple-100 border-2 border-purple-600' : 
                        'bg-gray-100'
                      }`}>
                        {index + 1}
                      </div>
                      <span className="ml-2 text-sm font-medium">{step.title}</span>
                    </div>
                    {index < steps.length - 1 && (
                      <div className={`flex-1 h-0.5 mx-4 ${index < store.currentStep ? 'bg-purple-600' : 'bg-gray-200'}`} />
                    )}
                  </React.Fragment>
                ))}
              </div>
            </div>
          </div>

          <div className="p-6">
            <CurrentStepComponent store={store} />
          </div>
        </div>
      </div>
    </div>
  );
});

export default DiffPage;
