'use client';

import React, { useState, useEffect, useRef } from 'react';
import { marked } from 'marked';
import { apiTableExtension, apiCallerExtension, renderComponents, clearComponents } from './marked-extensions';
import '../fs/md.css';
import './MarkdownPlus.css';

interface TocItem {
  id: string;
  level: number;
  text: string;
  element?: HTMLElement;
}

const HEADINGS = 'h1, h2, h3'; // 支持的标题标签

interface TenantInitPageProps {
  markdownFile?: string;
  content?: string;
}

const MarkdownPlus: React.FC<TenantInitPageProps> = ({
  markdownFile = '',
  content: directContent = ''
}) => {
  const [content, setContent] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>('');
  const [toc, setToc] = useState<TocItem[]>([]);
  const [activeSection, setActiveSection] = useState<string>('');
  const [showToc, setShowToc] = useState<boolean>(true);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // 如果直接传递了内容，使用直接内容
    if (directContent) {
      setContent(directContent);
      setLoading(false);
      return;
    }

    // 否则从文件加载
    if(!markdownFile) return;
    const fetchMarkdownContent = async () => {
      try {
        setLoading(true);

        const response = await fetch(markdownFile);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const text = await response.text();
        setContent(text);
      } catch (err) {
        console.error('获取Markdown文件失败:', err);
        setError('无法加载初始化文档，请检查文件是否存在。');
      } finally {
        setLoading(false);
      }
    };

    fetchMarkdownContent();
  }, [markdownFile, directContent]);

  // 生成目录和处理图片路径、API表格
  useEffect(() => {
    const fn = async () => {
      if (content && contentRef.current) {
        // 使用 marked 的 renderer hook 来处理图片路径
        const processedContent = content
        
        // 清理之前的组件实例
        clearComponents();
        
        // 配置 marked 选项和扩展
        marked.use({
          extensions: [apiTableExtension, apiCallerExtension],
          renderer: {
            image(token) {
              const { href, title, text } = token;

              // 处理图片路径：为本地图片统一添加 doc/ 前缀
              let processedHref = href;
              if (!href.startsWith('http://') && !href.startsWith('https://') && !href.startsWith('doc/')) {
                // 移除开头的 ./ 或 /
                const cleanHref = href.replace(/^\.?\//, '');
                processedHref = `doc/${cleanHref}`;
              }

              // 构建 img 标签
              const titleAttr = title ? ` title="${title}"` : '';
              return `<img src="${processedHref}" alt="${text}"${titleAttr} />`;
            }
          }
        });
        
        marked.setOptions({
          breaks: true,
          gfm: true
        });
        
        const htmlContent = await marked.parse(processedContent);
        
        contentRef.current.innerHTML = htmlContent;
        
        // 渲染所有扩展组件
        renderComponents(contentRef.current);
        
        // 获取所有标题元素
        const headings = contentRef.current.querySelectorAll(HEADINGS);
        const tocItems: TocItem[] = [];

        headings.forEach((heading, index) => {
          const level = parseInt(heading.tagName.charAt(1));
          const text = heading.textContent || '';
          const id = `heading-${index}`;
          
          heading.id = id;
          tocItems.push({
            id,
            level,
            text,
            element: heading as HTMLElement
          });
        });

        setToc(tocItems);
      }
    }
    fn();
  }, [content]);

  // 监听滚动位置，高亮当前章节
  useEffect(() => {
    const handleScroll = () => {
      if (!contentRef.current) return;

      const headings = contentRef.current.querySelectorAll('h1, h2, h3');
      let currentSection = '';

      headings.forEach((heading) => {
        const rect = heading.getBoundingClientRect();
        if (rect.top <= 100) {
          currentSection = heading.id;
        }
      });

      setActiveSection(currentSection);
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // 初始化

    return () => window.removeEventListener('scroll', handleScroll);
  }, [toc]);

  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h2 className="text-2xl font-bold text-gray-800 mb-2">加载失败</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
          >
            重新加载
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b sticky top-0 z-40">
        <div className="w-full px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <h1 className="text-2xl font-bold text-gray-900">
              PV Copilot 文档系统
            </h1>
            <button
              onClick={() => setShowToc(!showToc)}
              className="md:hidden bg-blue-500 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm markdown-plus-mobile-toggle"
            >
              {showToc ? '隐藏目录' : '显示目录'}
            </button> 
          </div>
        </div>
      </header>

      <div className="w-full px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex gap-8">
          {/* 目录侧边栏 */}
          <aside
            className={`${
              showToc ? 'block' : 'hidden'
            } md:block w-64 flex-shrink-0`}
          >
            <div className="sticky top-24 bg-white rounded-lg shadow-sm p-4 max-h-[calc(100vh-8rem)] overflow-y-auto markdown-plus-toc">
              <h3 className="font-semibold text-gray-900 mb-4 flex items-center">
                <span className="mr-2">📋</span>
                目录
              </h3>
              {toc.length > 0 && (
                <nav className="space-y-1">
                  {toc.map((item) => (
                    <button
                      key={item.id}
                      onClick={() => scrollToSection(item.id)}
                      className={`block w-full text-left px-2 py-1 text-sm rounded markdown-plus-toc-item ${
                        activeSection === item.id
                          ? 'bg-blue-100 text-blue-700 font-medium active'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`}
                      style={{
                        paddingLeft: `${(item.level - 1) * 12 + 8}px`
                      }}
                    >
                      {item.text}
                    </button>
                  ))}
                </nav>
              )}
            </div>
          </aside>

          {/* 主要内容 */}
          <main className="flex-1 min-w-0">
            <div className="bg-white rounded-lg shadow-sm">
              <div
                ref={contentRef}
                className="markdown-body"
              />
            </div>
          </main>
        </div>
      </div>

      {/* 回到顶部按钮 */}
      <button
        onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
        className="fixed bottom-8 right-8 bg-blue-500 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg z-30 markdown-plus-back-to-top"
        title="回到顶部"
      >
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
        </svg>
      </button>
 
    </div>
  );
};

export default MarkdownPlus;