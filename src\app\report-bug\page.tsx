"use client";

import React, { useState, useCallback, useMemo, useEffect } from "react";
import { useRouter } from "next/navigation";
import dayjs from "dayjs";
import Editor from "@/app/fs/ClientEditor";
import { message } from "@/utils/message"; // Import message utility

const QustionTpl = `
 

**问题描述：**


**复现步骤：**


**期望结果：**


**实际结果：**


**问题状态：** 
Open


**问题截图：** 
截图放置在此处


 
**(如有多个问题，请点击下方\`追加问题\`按钮)**

---

`

function getParams() {  
  const urlParams = new URLSearchParams(window.location.search);

  const studyNum = urlParams.get("studyNum") || "";
  const tenantId = urlParams.get("tenantId") || "";
  const tenantName = urlParams.get("tenantName") || "";
  const userId = urlParams.get("userId") || "";
  const userName = urlParams.get("userName") || "";
  const environment = urlParams.get("environment") || "";
  const currentLanguage = urlParams.get("currentLanguage") || "";
  const pdfMd5 = urlParams.get("pdfMd5") || "";
  const pdfName = urlParams.get("pdfName") || "";

  return {
    studyNum,
    tenantId,
    tenantName,
    userId,
    userName,
    environment,
    currentLanguage,
    pdfMd5,
    pdfName,
  } as any
}


const ReportBugPage: React.FC = () => {
  const router = useRouter();
  const [editorContent, setEditorContent] = useState("");
  const [email, setEmail] = useState("");
  const [submitter, setSubmitter] = useState("");

  useEffect(() => {
    const params = getParams();
    const now = new Date();
    const { studyNum, tenantId, tenantName, userId, userName, environment, currentLanguage, pdfMd5, pdfName } = params;
    if(userName){
      setSubmitter(userName)
    }
    // 使用 Markdown 格式生成默认内容
    const content = `
    
---

**请在此处详细描述您遇到的问题：**
| 字段          | 值                                      |
|---------------|-----------------------------------------|
| 研究编号      | ${studyNum || 'N/A'}                     |
| 租户ID        | ${tenantId || 'N/A'}                    |
| 租户名称      | ${tenantName || 'N/A'}                  |
| 用户ID        | ${userId || 'N/A'}                      |
| 用户名        | ${userName || 'N/A'}                    |
| 环境          | ${environment || 'N/A'}                 |
| 当前语言      | ${currentLanguage || 'N/A'}             |
| PDF名称       | ${pdfName || 'N/A'}                     |
| PDF校验码     | ${pdfMd5 || 'N/A'}                      |
| 时间          | ${dayjs(now).format('YYYY-MM-DD HH:mm:ss')} |
 
---
${QustionTpl}
`;
    setEditorContent(content);
  },[]);
 
  const handleSubmit = async () => {
    if (!editorContent) {
      message.warning("请编辑内容");
      return;
    }
    // 内容已经是 Markdown，不需要 HTML 格式化
    const params = getParams();
    const formData = new FormData(); 
    for (const key in params) {
      if (Object.prototype.hasOwnProperty.call(params, key)) {
        const element = params[key];
        formData.append(key, element)
      }
    }
    formData.append("content", editorContent); // 直接发送 Markdown 内容
    formData.append("email", email); // 添加邮箱字段
    formData.append("submitter", submitter); // 添加提交人字段

    try {
      const response = await fetch("api/bug-report", {
        method: "POST",
        body: formData,
      });
      const data = await response.json();
      if (response.ok) {
        message.success("Bug反馈提交成功");
        router.push("/fs?type=report&path=" + data.path );
      } else {
        // const errorText = await response.text();
        message.error(`提交失败: ${data.error}`);
      }
    } catch (error) {
      console.error("提交失败:", error);
      message.error("提交失败，请稍后重试");
    }
  };

  const handleAppend = async () => {
    setEditorContent((prevContent) => {
      return prevContent + QustionTpl;
    })
  }

  return (
    <div style={{ padding: "20px" }}>
      <h1>Bug反馈</h1>
      <br></br>

      {/* 添加邮箱和提交人输入框 - 两列布局 */}
      <div style={{
        marginBottom: "15px",
        display: "flex",
        gap: "15px",
        flexWrap: "wrap"
      }}>
        <div style={{ flex: "1", minWidth: "250px" }}>
          <label htmlFor="email" style={{
            display: "block",
            marginBottom: "3px",
            fontWeight: "bold",
            fontSize: "14px"
          }}>
            邮箱 (可选):
          </label>
          <input
            id="email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="请输入您的邮箱地址"
            style={{
              width: "100%",
              padding: "6px 10px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "14px",
              boxSizing: "border-box"
            }}
          />
        </div>

        <div style={{ flex: "1", minWidth: "250px" }}>
          <label htmlFor="submitter" style={{
            display: "block",
            marginBottom: "3px",
            fontWeight: "bold",
            fontSize: "14px"
          }}>
            提交人 (可选):
          </label>
          <input
            id="submitter"
            type="text"
            value={submitter}
            onChange={(e) => setSubmitter(e.target.value)}
            placeholder="请输入提交人姓名"
            style={{
              width: "100%",
              padding: "6px 10px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              fontSize: "14px",
              boxSizing: "border-box"
            }}
          />
        </div>
      </div>

      <Editor
        data={editorContent}
        onChange={(event, editor) =>
          setEditorContent(editor.getData())
        }
      />
      
      <button
        onClick={handleSubmit}
        className="bg-blue-500 hover:bg-blue-700 cursor-pointer text-white font-bold py-2 px-4 rounded mt-5"
      >
        提交
      </button>

      <button
        onClick={handleAppend}
        className="ml-3 bg-blue-400 hover:bg-blue-500 cursor-pointer text-white font-bold py-2 px-4 rounded mt-5"
      >
        追加问题
      </button>
    </div>
  );
};

export default ReportBugPage;
