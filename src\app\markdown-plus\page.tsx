'use client';

import MarkdownPlus from './MarkdownPlus'
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function Markdown() {
  const searchParams = useSearchParams();
  const [file, setFile] = useState('');
  
  useEffect(() => {
    const fileParam = searchParams.get('file') || 'demo.md';
    document.title = fileParam.replace('.md', '');
    setFile(fileParam);
  }, [searchParams]);  
  
  return <MarkdownPlus markdownFile={file} />;
}
 