{"name": "recognition-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "docker:build": "docker build -t pv-manus-front . --network=host", "start": "next start", "lint": "next lint", "zip": "git ls-files | zip project.zip -@"}, "dependencies": {"@ckeditor/ckeditor5-react": "^9.5.0", "@microlink/react-json-view": "^1.26.2", "@radix-ui/react-slot": "^1.2.2", "@thumbmarkjs/thumbmarkjs": "^0.20.0", "ckeditor5": "45.0.0", "ckeditor5-premium-features": "45.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "immer": "^10.1.1", "jsondiffpatch": "^0.7.3", "jsonpath": "^1.1.1", "jszip": "^3.10.1", "lodash": "^4.17.21", "lucide-react": "^0.509.0", "marked": "^15.0.7", "mime-types": "^3.0.1", "mobx": "^6.13.7", "mobx-react-lite": "^4.1.0", "next": "15.2.4", "node-cron": "^3.0.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-icons": "^5.5.0", "shelljs": "^0.9.2", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.3", "@types/jsonpath": "^0.2.4", "@types/lodash": "^4.17.16", "@types/mime-types": "^2.1.4", "@types/node": "^20", "@types/node-cron": "^3.0.11", "@types/react": "^19", "@types/react-dom": "^19", "@types/shelljs": "^0.8.15", "eslint": "^9", "eslint-config-next": "15.2.4", "postcss": "^8.5.3", "tailwindcss": "^4.1.3", "tw-animate-css": "^1.2.9", "typescript": "^5"}}