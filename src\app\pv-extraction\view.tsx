"use client";
import React, { useState, useCallback, useMemo, useEffect, useRef } from "react";
import { createPortal } from "react-dom";
import { useRouter, useSearchParams } from "next/navigation";
import dayjs from "dayjs";
import { message } from "@/utils/message";
import { SchemaForm } from "@/components/schema-form/SchemaForm"; 
import { validate, ERROR, RULE } from '@/utils/validate';
import { MdOutlineError } from "react-icons/md";
import { IoRefreshCircle } from "react-icons/io5";

const rules:RULE[] = [
  {
    path: '$..cdr_lot_no',
    message: 'cdr_lot_no 长度不能超过"35"个字符',
    validater: (value: string,data: any, path: string) => { 
      if(!value) return true;
      return value.length <= 35;
    }
  },
]
 

const Page: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const key = searchParams.get("key") || "";

  const [data, setData] = useState({});
  const [errors, setErrors] = useState<ERROR[]>([]);

  useEffect(() => {
    if (!key) return;
    const d = JSON.parse(localStorage.getItem(key) || "{}");      
    setData(d);
    const v = validate(d,rules);
    if (v && v.length > 0) { 
      setErrors(v);
      message.error("数据校验失败，请检查！");
      return;
    }  
  }, [key]);

  
  const handleValidate = useCallback(() => {
    const v = validate(data,rules);
    if (v && v.length > 0) { 
      setErrors(v);
      message.error("数据校验失败，请检查！");
      return;
    }
    setErrors([]);
    localStorage.setItem(key, JSON.stringify(data));
    message.success("数据校验成功！数据已保存,关闭本页面在识别工具中查看数据。",{duration: 8000});
  }, [data,key]);

  const handleChange = (data:any, path?: string, value?: string) => { 
  };

  

  if (Object.keys(data).length === 0) {
    return null;
  }

  return (
    <div className="p-5">
      <div className="mb-4 p-3 bg-blue-50 border-l-4 border-blue-500 rounded-md">
        <p className="text-sm text-gray-700 flex items-center gap-2">
          <span className="font-medium">提示:</span>
          <span className="text-red-500"><MdOutlineError className="inline mr-1" size={18} /></span>
          红色图标查看错误信息，
          <span className="text-green-700"><IoRefreshCircle className="inline mr-1" size={18} /></span>
          点击绿色图标进行数据校验，校验通过后自动保存数据
        </p>
      </div>
      <SchemaForm readonly={false} data={data} onChange={handleChange} />
      {(errors && errors.length > 0) && (
        <ErrorIndicator errors={errors} />
      )}
      <div 
        className="fixed top-1 right-16 cursor-pointer z-[1000]"
        onClick={handleValidate}
      >
        <span title="执行校验" className="text-green-700 text-3xl font-bold"><IoRefreshCircle /></span>
      </div>
    </div>
  );
};

const ErrorIndicator: React.FC<{ errors: ERROR[] }> = ({ errors }) => {
  const [showTooltip, setShowTooltip] = useState(false);
  const indicatorRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);

  const handleMouseEnter = () => {
    setShowTooltip(true);
  };

  const handleMouseLeave = (e: React.MouseEvent) => {
    const relatedTarget = e.relatedTarget as HTMLElement;
    if (!tooltipRef.current?.contains(relatedTarget)) {
      setShowTooltip(false);
    }
  };

  const handleTooltipMouseLeave = (e: React.MouseEvent) => {
    const relatedTarget = e.relatedTarget as HTMLElement;
    if (!indicatorRef.current?.contains(relatedTarget)) {
      setShowTooltip(false);
    }
  };

  const handleClick = (error: ERROR) => { 
    const el = document.querySelector<HTMLDivElement>(`[data-path="${error.path}"]`);
    if(el) {
      el.scrollIntoView({ block:'center',inline:'center'});
      const targetElement = el.parentElement || el;
      targetElement.classList.add("border-2", "border-red-500");
      setTimeout(() => {
        targetElement.classList.remove("border-2", "border-red-500");
        setTimeout(() => {
          targetElement.classList.add("border-2", "border-red-500");
          setTimeout(() => {
        targetElement.classList.remove("border-2", "border-red-500");
          }, 300);
        }, 300);
      }, 300);
    }
    
  }

  return (
    <>
      <div
        ref={indicatorRef}
        className="fixed top-1 right-6 cursor-pointer z-[1000]"
        onMouseEnter={handleMouseEnter}
      >
        <span title="查看错误" className="text-red-500 text-3xl font-bold"><MdOutlineError /></span>
      </div>
      {showTooltip && createPortal(
        <div
          ref={tooltipRef}
          className="fixed top-14 right-5 p-3 bg-white shadow-lg rounded-lg z-[1001] max-w-[300px] max-h-[400px] overflow-y-auto"
          onMouseLeave={handleTooltipMouseLeave}
        >
          <ul className="list-disc pl-4 space-y-2">
            {errors.map((error, index) => (
              <li key={index} onClick={() => handleClick(error)} className="text-red-500 cursor-pointer hover:bg-blue-100">
                {error.message}
              </li>
            ))}
          </ul>
        </div>,
        document.body
      )}
    </>
  );
};

export default Page;
