import React, { useEffect } from 'react';
import { parse } from 'marked';
import '../fs/md.css';

interface MarkdownViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  content: string;
}

export const MarkdownViewModal: React.FC<MarkdownViewModalProps> = ({ 
  isOpen, 
  onClose, 
  title, 
  content 
}) => {
  // 管理body滚动状态
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  // 解析markdown内容
  const parsedContent = parse(content || '');
  
  return (
    <div className="fixed inset-0 flex items-center justify-center z-50" onKeyDown={handleKeyDown}>
      <div className="fixed inset-0 bg-black/50" onClick={onClose}></div>
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] relative z-10 flex flex-col">
        <div className="flex justify-between items-center p-5 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-2xl leading-none"
          >
            &times;
          </button>
        </div>
        <div className="flex-1 overflow-y-auto p-5">
          <div 
            className="markdown-body prose max-w-none"
            dangerouslySetInnerHTML={{ __html: parsedContent }}
          />
        </div>
        <div className="flex justify-end p-5 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  );
};
