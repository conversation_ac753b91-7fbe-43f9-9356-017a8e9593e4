import React from 'react';
import { observer } from 'mobx-react-lite';
import DiffStore from '../stores/diffStore';

interface PrimaryKeyConfigProps {
  store: DiffStore;
}

const PrimaryKeyConfig: React.FC<PrimaryKeyConfigProps> = observer(({ store }) => {
  // 获取数组对象的所有可能字段
  const getAvailableKeys = (path: string): string[] => {
    const array = store.getValueByPath(store.originalReport, path) as any[];
    if (!array || array.length === 0) return [];
    
    // 收集所有对象的所有键
    const allKeys = new Set<string>();
    array.forEach(item => {
      if (item && typeof item === 'object') {
        Object.keys(item).forEach(key => allKeys.add(key));
      }
    });
    
    return Array.from(allKeys).sort();
  };

  // 获取数组预览数据
  const getArrayPreview = (path: string) => {
    const array = store.getValueByPath(store.originalReport, path) as any[];
    if (!array || array.length === 0) return [];
    
    // 返回前3个项目作为预览
    return array.slice(0, 3);
  };

  // 获取主键配置验证结果
  const getValidationResult = (path: string) => {
    const config = store.primaryKeyConfigs.find(c => c.path === path);
    if (!config) return null;
    
    return store.validatePrimaryKeyConfig(path, config.keys);
  };

  // 处理主键选择变化
  const handleKeyChange = (path: string, key: string, checked: boolean) => {
    const config = store.primaryKeyConfigs.find(c => c.path === path);
    if (!config) return;
    
    let newKeys = [...config.keys];
    if (checked) {
      if (!newKeys.includes(key)) {
        newKeys.push(key);
      }
    } else {
      newKeys = newKeys.filter(k => k !== key);
    }
    
    store.setPrimaryKeyConfig(path, newKeys);
  };

  if (store.loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">加载数据中...</span>
      </div>
    );
  }

  if (store.error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">加载失败</h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{store.error}</p>
            </div>
            <div className="mt-4">
              <button
                onClick={() => store.loadSampleData()}
                className="bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200"
              >
                重新加载
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="text-lg font-medium text-blue-900 mb-2">步骤 1: 主键配置</h3>
        <p className="text-blue-700">
          为数组对象配置主键字段，用于在对比时正确关联相同的记录。系统已自动检测可能的主键字段，您可以根据需要调整。
        </p>
      </div>

      {store.primaryKeyConfigs.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <p>未检测到数组对象数据</p>
        </div>
      ) : (
        <div className="space-y-6">
          {store.primaryKeyConfigs.map((config, index) => {
            const availableKeys = getAvailableKeys(config.path);
            const previewData = getArrayPreview(config.path);
            
            return (
              <div key={config.path} className="border border-gray-200 rounded-lg p-6">
                <div className="mb-4">
                  <h4 className="text-lg font-medium text-gray-900 mb-2">
                    数组路径: <code className="bg-gray-100 px-2 py-1 rounded text-sm">{config.path}</code>
                  </h4>
                  <p className="text-sm text-gray-600">
                    共 {store.getValueByPath(store.originalReport, config.path)?.length || 0} 条记录
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* 主键选择 */}
                  <div>
                    <h5 className="font-medium text-gray-900 mb-3">选择主键字段</h5>
                    <div className="space-y-2 max-h-48 overflow-y-auto">
                      {availableKeys.map(key => (
                        <label key={key} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={config.keys.includes(key)}
                            onChange={(e) => handleKeyChange(config.path, key, e.target.checked)}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                          <span className="ml-2 text-sm text-gray-700">{key}</span>
                        </label>
                      ))}
                    </div>
                    
                    {/* 主键验证结果 */}
                    {(() => {
                      const validation = getValidationResult(config.path);
                      if (validation) {
                        return (
                          <div className={`text-sm mt-2 p-2 rounded ${validation.valid ? 'bg-green-50 text-green-700' : 'bg-amber-50 text-amber-700'}`}>
                            {validation.message}
                          </div>
                        );
                      }
                      return null;
                    })()}
                    
                    {config.keys.length === 0 && (
                      <p className="text-sm text-amber-600 mt-2">
                        ⚠️ 未选择主键，将使用数组索引进行匹配
                      </p>
                    )}
                  </div>

                  {/* 数据预览 */}
                  <div>
                    <h5 className="font-medium text-gray-900 mb-3">数据预览</h5>
                    <div className="bg-gray-50 rounded-lg p-3 max-h-48 overflow-auto">
                      {previewData.length > 0 ? (
                        <div className="space-y-2">
                          {previewData.map((item, idx) => (
                            <div key={idx} className="bg-white rounded p-2 text-xs">
                              <div className="font-medium text-gray-600 mb-1">记录 {idx + 1}:</div>
                              {config.keys.length > 0 && (
                                <div className="text-blue-600 mb-1">
                                  主键: {config.keys.map(k => item[k]).join(' | ')}
                                </div>
                              )}
                              <div className="text-gray-500 truncate">
                                {JSON.stringify(item).substring(0, 100)}...
                              </div>
                            </div>
                          ))}
                          {store.getValueByPath(store.originalReport, config.path)?.length > 3 && (
                            <div className="text-gray-500 text-center">
                              ... 还有 {store.getValueByPath(store.originalReport, config.path)?.length - 3} 条记录
                            </div>
                          )}
                        </div>
                      ) : (
                        <p className="text-gray-500">无数据</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}

      <div className="flex justify-end pt-6 border-t border-gray-200">
        <button
          onClick={() => store.nextStep()}
          disabled={store.loading}
          className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          下一步：计算差异
        </button>
      </div>
    </div>
  );
});

export default PrimaryKeyConfig;
