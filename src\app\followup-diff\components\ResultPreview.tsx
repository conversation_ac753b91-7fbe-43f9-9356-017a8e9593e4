import React, { useState } from 'react';
import { observer } from 'mobx-react-lite';
import { toJS } from 'mobx';
import DiffStore from '../stores/diffStore';

interface ResultPreviewProps {
  store: DiffStore;
}

const ResultPreview: React.FC<ResultPreviewProps> = observer(({ store }) => {
  const [copied, setCopied] = useState(false);
  const finalResult = store.generateFinalResult();
  
  // 复制结果到剪贴板
  const copyToClipboard = () => {
    navigator.clipboard.writeText(JSON.stringify(finalResult, null, 2))
      .then(() => {
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      })
      .catch(err => {
        console.error('复制失败:', err);
        alert('复制失败，请手动复制');
      });
  };

  // 下载结果为JSON文件
  const downloadResult = () => {
    const blob = new Blob([JSON.stringify(finalResult, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'merged-report.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 自动录入功能
  const handleAutoEntry = () => {
    console.log('自动录入按钮被点击');
    const argusDiff = toJS(store.argusDiff);  
    const report = toJS(store.argusReport);  
    const msg = { 
      action: 'followUpstrucutrueData', 
      data: {
        diff:argusDiff,
        report,
      },
      md5:'' 
    };
    console.log('准备发送的数据:', JSON.stringify(msg, null, 2));

    window.postMessage(msg, '*');

    alert('数据已发送到父系统进行自动录入');
  };

  // 格式化差异统计
  const formatDiffStats = () => {
    const total = store.diffItems.length;
    const confirmed = store.diffItems.filter(item => item.confirmed).length;
    const modified = store.diffItems.filter(item => item.modified).length;
    const added = store.diffItems.filter(item => item.oldValue === undefined).length;
    const changed = store.diffItems.filter(item => item.oldValue !== undefined).length;
    
    return { total, confirmed, modified, added, changed };
  };

  const formatArgusDiffStats = () => {
    const total = store.argusDiff.length;
    const modified = store.argusDiff.filter(item => item.type === 'modified').length;
    const added = store.argusDiff.filter(item => item.type === 'added').length;
    const deleted = store.argusDiff.filter(item => item.type === 'deleted').length;

    return { total, modified, added, deleted };
  };

  const stats = formatDiffStats();
  const argusStats = store.argusReport ? formatArgusDiffStats() : null;

  return (
    <div className="space-y-6">
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="text-lg font-medium text-blue-900 mb-2">步骤 3: 结果预览</h3>
        <p className="text-blue-700">
          预览合并后的最终结果。您可以复制或下载结果数据。
        </p>
      </div>

      {/* 差异统计 */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="text-lg font-medium text-gray-900 mb-3">差异统计</h4>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
            <div className="text-sm text-gray-600">总差异数</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-purple-600">{stats.confirmed}</div>
            <div className="text-sm text-gray-600">已确认</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-amber-600">{stats.modified}</div>
            <div className="text-sm text-gray-600">手动修改</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-600">{stats.added}</div>
            <div className="text-sm text-gray-600">新增项</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-blue-600">{stats.changed}</div>
            <div className="text-sm text-gray-600">修改项</div>
          </div>
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="flex gap-3">
        <button
          onClick={copyToClipboard}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
            <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
          </svg>
          {copied ? '已复制!' : '复制到剪贴板'}
        </button>
        <button
          onClick={downloadResult}
          className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
          下载JSON
        </button>
        <button
          onClick={handleAutoEntry}
          className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 flex items-center"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z" clipRule="evenodd" />
          </svg>
          自动录入
        </button>
      </div>

      {/* Argus报告差异 */}
      {store.argusReport && (
        <div className="border border-purple-200 rounded-lg">
          <div className="bg-purple-50 px-4 py-3 border-b border-purple-200">
            <div className="flex justify-between items-center mb-2">
              <h4 className="font-medium text-purple-900">Argus报告差异</h4>
              <div className="text-sm text-purple-600">
                {argusStats?.total || 0} 个差异
              </div>
            </div>
            {/* 差异统计 */}
            {argusStats && argusStats.total > 0 && (
              <div className="flex gap-4 text-sm">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-green-500 rounded-full mr-1"></div>
                  <span>新增: {argusStats.added}</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-blue-500 rounded-full mr-1"></div>
                  <span>修改: {argusStats.modified}</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-red-500 rounded-full mr-1"></div>
                  <span>删除: {argusStats.deleted}</span>
                </div>
              </div>
            )}
          </div>
          <div className="p-4 max-h-[400px] overflow-auto">
            {store.argusDiff.length === 0 ? (
              <p className="text-sm text-gray-500">无差异</p>
            ) : (
              <div className="space-y-3">
                {store.argusDiff.map((diff, index) => {
                  const getTypeConfig = (type: string) => {
                    switch (type) {
                      case 'added':
                        return {
                          bgColor: 'bg-green-50',
                          borderColor: 'border-green-200',
                          textColor: 'text-green-800',
                          icon: '➕',
                          label: '新增'
                        };
                      case 'modified':
                        return {
                          bgColor: 'bg-blue-50',
                          borderColor: 'border-blue-200',
                          textColor: 'text-blue-800',
                          icon: '✏️',
                          label: '修改'
                        };
                      case 'deleted':
                        return {
                          bgColor: 'bg-red-50',
                          borderColor: 'border-red-200',
                          textColor: 'text-red-800',
                          icon: '➖',
                          label: '删除'
                        };
                      default:
                        return {
                          bgColor: 'bg-gray-50',
                          borderColor: 'border-gray-200',
                          textColor: 'text-gray-800',
                          icon: '❓',
                          label: '未知'
                        };
                    }
                  };

                  const config = getTypeConfig(diff.type);

                  return (
                    <div key={index} className={`border ${config.borderColor} ${config.bgColor} rounded-lg p-3`}>
                      <div className="flex items-center mb-2">
                        <span className="mr-2">{config.icon}</span>
                        <span className={`font-medium ${config.textColor} text-sm`}>
                          {config.label}
                        </span>
                        <span className="ml-2 text-xs text-gray-600 font-mono">
                          {diff.path}
                        </span>
                      </div>

                      {diff.description && (
                        <div className="text-sm text-gray-700 mb-2">
                          {diff.description}
                        </div>
                      )}

                      {diff.type === 'added' && (
                        <div className="bg-green-100 p-2 rounded text-green-800 text-sm">
                          <span className="font-medium">新增值:</span>
                          <pre className="mt-1 whitespace-pre-wrap">{JSON.stringify(diff.newValue, null, 2)}</pre>
                        </div>
                      )}

                      {diff.type === 'deleted' && (
                        <div className="bg-red-100 p-2 rounded text-red-800 text-sm">
                          <span className="font-medium">删除值:</span>
                          <pre className="mt-1 whitespace-pre-wrap">{JSON.stringify(diff.oldValue, null, 2)}</pre>
                        </div>
                      )}

                      {diff.type === 'modified' && (
                        <div className="grid grid-cols-2 gap-2">
                          <div className="bg-red-100 p-2 rounded text-red-800 text-sm">
                            <span className="font-medium">原值:</span>
                            <pre className="mt-1 whitespace-pre-wrap">{JSON.stringify(diff.oldValue, null, 2)}</pre>
                          </div>
                          <div className="bg-green-100 p-2 rounded text-green-800 text-sm">
                            <span className="font-medium">新值:</span>
                            <pre className="mt-1 whitespace-pre-wrap">{JSON.stringify(diff.newValue, null, 2)}</pre>
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      )}

      {/* 结果预览 */}
      <div className="border border-gray-200 rounded-lg">
        <div className="bg-gray-50 px-4 py-3 border-b border-gray-200 flex justify-between items-center">
          <h4 className="font-medium text-gray-900">
            {store.argusReport ? 'Argus报告结果' : '最终结果'}
          </h4>
          <div className="text-sm text-gray-500">
            {Object.keys(store.argusReport || finalResult).length} 个顶级字段
          </div>
        </div>
        <div className="p-4 max-h-[500px] overflow-auto">
          <pre className="text-sm text-gray-800 whitespace-pre-wrap">
            {JSON.stringify(store.argusReport || finalResult, null, 2)}
          </pre>
        </div>
      </div>

      {/* 导航按钮 */}
      <div className="flex justify-between pt-6 border-t border-gray-200">
        <button
          onClick={() => store.prevStep()}
          className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700"
        >
          上一步
        </button>
        <button
          onClick={() => store.reset()}
          className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
        >
          重新开始
        </button>
      </div>
    </div>
  );
});

export default ResultPreview;
