import { makeAutoObservable, runInAction } from 'mobx';
import * as jsondiffpatch from 'jsondiffpatch';

// 主键配置接口
export interface PrimaryKeyConfig {
  path: string;
  keys: string[];
}

// 差异项接口
export interface DiffItem {
  path: string;
  oldValue: any;
  newValue: any;
  confirmed: boolean;
  modified: boolean;
  userValue: any;
}

// 步骤枚举
export enum DiffStep {
  PrimaryKeyConfig = 0,
  DiffConfirmation = 1,
  ArgusMapping = 2,
  ResultPreview = 3
}

class DiffStore {
  // 原始数据
  originalReport: any = null;
  followupReport: any = null;
  
  // 主键配置
  primaryKeyConfigs: PrimaryKeyConfig[] = [];
  
  // 差异结果
  diffResult: any = null;
  diffItems: DiffItem[] = [];
  
  // 当前步骤
  currentStep: DiffStep = DiffStep.PrimaryKeyConfig;
  
  // 加载状态
  loading: boolean = false;
  error: string | null = null;

  constructor() {
    makeAutoObservable(this);
    this.loadSampleData();
  }

  // 加载示例数据
  async loadSampleData() {
    this.setLoading(true);
    try {
      const originalRes = await fetch('followup-diff/data/report-000.json');
      const followupRes = await fetch('followup-diff/data/report-001.json');
      
      if (!originalRes.ok || !followupRes.ok) {
        throw new Error('加载示例数据失败');
      }
      
      const originalData = await originalRes.json();
      const followupData = await followupRes.json();
      
      runInAction(() => {
        this.originalReport = originalData;
        this.followupReport = followupData;
        this.error = null;
        
        // 自动检测可能的数组对象路径并生成默认主键配置
        this.detectArrayObjectPaths();
      });
    } catch (error) {
      runInAction(() => {
        this.error = error instanceof Error ? error.message : '未知错误';
      });
    } finally {
      this.setLoading(false);
    }
  }

  // 检测数据中的数组对象路径
  detectArrayObjectPaths() {
    const paths: string[] = [];
    
    const traverse = (obj: any, path: string = '') => {
      if (!obj || typeof obj !== 'object') return;
      
      if (Array.isArray(obj) && obj.length > 0 && typeof obj[0] === 'object' && !Array.isArray(obj[0])) {
        paths.push(path);
      }
      
      for (const key in obj) {
        traverse(obj[key], path ? `${path}.${key}` : key);
      }
    };
    
    traverse(this.originalReport);
    
    // 为每个数组对象路径创建默认主键配置
    this.primaryKeyConfigs = paths.map(path => {
      const array = this.getValueByPath(this.originalReport, path) as any[];
      if (!array || array.length === 0) return { path, keys: [] };
      
      // 分析字段唯一性并选择最佳主键
      const bestKeys = this.selectBestPrimaryKeys(array);
      
      return {
        path,
        keys: bestKeys
      };
    });
  }

  // 分析数组并选择最佳主键
  private selectBestPrimaryKeys(array: any[]): string[] {
    if (!array || array.length === 0) return [];
    
    const firstItem = array[0];
    if (!firstItem || typeof firstItem !== 'object') return [];
    
    const allKeys = Object.keys(firstItem);
    const keyScores: {key: string, uniqueness: number}[] = [];
    
    // 计算每个字段的唯一性得分
    for (const key of allKeys) {
      const values = array.map(item => item[key]).filter(v => v != null && v !== '');
      const uniqueValues = new Set(values);
      const uniqueness = values.length > 0 ? uniqueValues.size / values.length : 0;
      
      // 优先选择包含id、编号、名称等关键字的字段
      let priority = 1;
      if (key.toLowerCase().includes('id')) priority = 10;
      else if (key.toLowerCase().includes('编号')) priority = 9;
      else if (key.toLowerCase().includes('名称')) priority = 8;
      else if (key.toLowerCase().includes('批号')) priority = 7;
      
      keyScores.push({key, uniqueness: uniqueness * priority});
    }
    
    // 按得分排序，选择得分最高的1-2个字段
    keyScores.sort((a, b) => b.uniqueness - a.uniqueness);
    
    const selectedKeys = keyScores
      .filter(score => score.uniqueness > 0.5)
      .slice(0, 2)
      .map(score => score.key);
    
    return selectedKeys;
  }

  // 验证主键配置的有效性
  validatePrimaryKeyConfig(path: string, keys: string[]): {valid: boolean, uniqueness: number, message: string} {
    const array = this.getValueByPath(this.originalReport, path) as any[];
    if (!array || array.length === 0) {
      return {valid: false, uniqueness: 0, message: '数组为空'};
    }
    
    if (keys.length === 0) {
      return {valid: false, uniqueness: 0, message: '未选择主键字段'};
    }
    
    const uniqueValues = new Set();
    array.forEach(item => {
      const keyValue = keys.map(k => String(item[k] || '')).join('|');
      uniqueValues.add(keyValue);
    });
    
    const uniqueness = uniqueValues.size / array.length;
    const valid = uniqueness === 1;
    
    return {
      valid,
      uniqueness,
      message: valid
        ? '✅ 主键唯一性良好'
        : `⚠️ 唯一性: ${(uniqueness * 100).toFixed(1)}%，可能存在重复记录`
    };
  }

  // 根据路径获取对象中的值
  getValueByPath(obj: any, path: string): any {
    if (!path) return obj;
    return path.split('.').reduce((o, key) => (o && o[key] !== undefined) ? o[key] : undefined, obj);
  }

  // 设置主键配置
  setPrimaryKeyConfig(path: string, keys: string[]) {
    const index = this.primaryKeyConfigs.findIndex(config => config.path === path);
    if (index >= 0) {
      this.primaryKeyConfigs[index].keys = keys;
    } else {
      this.primaryKeyConfigs.push({ path, keys });
    }
  }

  // 计算差异
  calculateDiff() {
    this.setLoading(true);

    try {
      // 使用路径感知的diff算法
      this.diffResult = this.calculatePathAwareDiff();
      
      // 添加调试日志
      // console.log('差异结果:', JSON.stringify(this.diffResult, null, 2));
      // console.log('主键配置:', this.primaryKeyConfigs);

      // 转换差异结果为可操作的项目列表
      this.convertDiffToItems();
    } catch (error) {
      runInAction(() => {
        this.error = error instanceof Error ? error.message : '计算差异时出错';
      });
    } finally {
      this.setLoading(false);
    }
  }

  // 路径感知的diff算法
  private calculatePathAwareDiff(): any {
    const result: any = {};
    
    // 为每个配置了主键的数组路径创建专门的diff实例
    const pathConfigs = new Map<string, PrimaryKeyConfig>();
    this.primaryKeyConfigs.forEach(config => {
      if (config.keys.length > 0) {
        pathConfigs.set(config.path, config);
      }
    });
    
    // 递归比较两个对象，对数组使用主键匹配
    const deepDiff = (oldObj: any, newObj: any, currentPath: string = ''): any => {
      if (oldObj === newObj) return undefined;
      
      if (Array.isArray(oldObj) && Array.isArray(newObj)) {
        // 检查是否有主键配置
        const config = pathConfigs.get(currentPath);
        if (config && config.keys.length > 0) {
          return this.diffArrayWithKeys(oldObj, newObj, config);
        } else {
          // 使用标准diff
          const diffpatcher = jsondiffpatch.create();
          return diffpatcher.diff(oldObj, newObj);
        }
      }
      
      if (typeof oldObj === 'object' && typeof newObj === 'object' && oldObj !== null && newObj !== null) {
        const diff: any = {};
        
        // 处理所有键
        const allKeys = new Set([...Object.keys(oldObj || {}), ...Object.keys(newObj || {})]);
        
        for (const key of Array.from(allKeys)) {
          const newPath = currentPath ? `${currentPath}.${key}` : key;
          const oldVal = oldObj[key];
          const newVal = newObj[key];
          
          const subDiff = deepDiff(oldVal, newVal, newPath);
          if (subDiff !== undefined) {
            diff[key] = subDiff;
          }
        }
        
        return Object.keys(diff).length > 0 ? diff : undefined;
      }
      
      // 基本类型比较
      return oldObj === newObj ? undefined : [oldObj, newObj];
    };
    
    return deepDiff(this.originalReport, this.followupReport);
  }

  // 使用主键匹配数组
  private diffArrayWithKeys(oldArray: any[], newArray: any[], config: PrimaryKeyConfig): any {
    const oldMap = new Map<string, any>();
    const newMap = new Map<string, any>();
    
    // 为旧数组创建主键映射
    oldArray.forEach((item, index) => {
      const key = this.generateKeyForItem(item, config.keys);
      oldMap.set(key, {item, index});
    });
    
    // 为新数组创建主键映射
    newArray.forEach((item, index) => {
      const key = this.generateKeyForItem(item, config.keys);
      newMap.set(key, {item, index});
    });
    
    const diff: any = {};
    
    // 处理修改 - 现在支持字段级别差异
    for (const key of Array.from(oldMap.keys())) {
      if (newMap.has(key)) {
        const oldData = oldMap.get(key)!;
        const newData = newMap.get(key)!;
        const itemDiff = this.diffObjectsWithKeys(oldData.item, newData.item, config.path);
        if (itemDiff) {
          // 存储字段级别的差异
          diff[`_${oldData.index}`] = {
            _type: 'modified',
            _key: key,
            _index: oldData.index,
            _oldItem: oldData.item,
            _newItem: newData.item,
            _fields: itemDiff
          };
        }
      }
    }
    
    // 处理新增
    for (const key of Array.from(newMap.keys())) {
      if (!oldMap.has(key)) {
        const newData = newMap.get(key)!;
        const index = newArray.findIndex(item => this.generateKeyForItem(item, config.keys) === key);
        diff[`_${index}`] = {
          _type: 'added',
          _key: key,
          _index: index,
          _newItem: newData.item
        };
      }
    }
    
    return Object.keys(diff).length > 0 ? diff : undefined;
  }

  // 为对象生成主键
  private generateKeyForItem(item: any, keys: string[]): string {
    if (!item || typeof item !== 'object') return '';
    
    const keyParts = keys.map(key => String(item[key] || ''));
    return keyParts.join('|');
  }

  // 比较两个对象
  private diffObjectsWithKeys(oldObj: any, newObj: any, path: string): any {
    const diffpatcher = jsondiffpatch.create();
    return diffpatcher.diff(oldObj, newObj);
  }

  // 处理字段级差异的辅助方法
  private processFieldLevelDiff(fieldDiff: any, basePath: string, items: DiffItem[]) {
    const processNestedDiff = (diffObj: any, currentPath: string) => {
      for (const [field, change] of Object.entries(diffObj)) {
        const fieldPath = `${currentPath}.${field}`;
        
        if (Array.isArray(change) && change.length === 2) {
          // 字段级修改 [oldValue, newValue]
          items.push({
            path: fieldPath,
            oldValue: change[0],
            newValue: change[1],
            confirmed: false,
            modified: false,
            userValue: change[1]
          });
        } else if (typeof change === 'object' && change !== null && !Array.isArray(change)) {
          // 嵌套对象继续递归
          processNestedDiff(change, fieldPath);
        }
      }
    };
    
    processNestedDiff(fieldDiff, basePath);
  }

  // 将差异结果转换为项目列表
  convertDiffToItems() {
    const items: DiffItem[] = [];

    const traverse = (diff: any, path: string = '') => {
      if (!diff || typeof diff !== 'object') return;

      for (const key in diff) {
        const currentPath = path ? `${path}.${key}` : key;
        const value = diff[key];

        // 处理新的数组差异格式
        if (value && typeof value === 'object' && !Array.isArray(value)) {
          if (value._type === 'modified') {
            // 处理数组项的字段级修改
            const { _fields } = value;
            this.processFieldLevelDiff(_fields, `${path}[${value._index}]`, items);
          } else if (value._type === 'added') {
            // 处理新增项
            items.push({
              path: `${path}[${value._index}]`,
              oldValue: undefined,
              newValue: value._newItem,
              confirmed: false,
              modified: false,
              userValue: value._newItem
            });
          } else {
            // 递归处理嵌套对象
            traverse(value, currentPath);
          }
        } else if (Array.isArray(value)) {
          // 处理旧的差异格式（向后兼容）
          if (value.length === 3 && value[2] === 0 && value[1] !== 0) {
            items.push({
              path: currentPath,
              oldValue: value[0],
              newValue: value[1],
              confirmed: false,
              modified: false,
              userValue: value[1]
            });
          } else if (value.length === 1 && Array.isArray(value[0]) && value[0].length === 2 && value[0][0] === '+') {
            items.push({
              path: currentPath,
              oldValue: undefined,
              newValue: value[0][1],
              confirmed: false,
              modified: false,
              userValue: value[0][1]
            });
          }
        } else if (typeof value === 'object' && !Array.isArray(value)) {
          // 递归处理嵌套对象
          traverse(value, currentPath);
        }
      }
    };

    traverse(this.diffResult);
    
    // 过滤无效项
    this.diffItems = items.filter(item =>
      item.newValue !== undefined &&
      item.newValue !== 0 &&
      !(typeof item.newValue === 'object' && item.newValue !== null && Object.keys(item.newValue).length === 0)
    );

    // 添加调试日志
    console.log('处理后的差异项:', this.diffItems);
    console.log('字段级差异总数:', this.diffItems.length);
  }

  // 确认差异项
  confirmDiffItem(path: string, confirmed: boolean) {
    const item = this.diffItems.find(item => item.path === path);
    if (item) {
      item.confirmed = confirmed;
    }
  }

  // 修改差异项的值
  modifyDiffItem(path: string, value: any) {
    const item = this.diffItems.find(item => item.path === path);
    if (item) {
      item.userValue = value;
      item.modified = true;
      item.confirmed = true;
    }
  }

  // 生成最终结果
  generateFinalResult() {
    // 从随访报告开始，因为我们要应用所有的变更
    const result = JSON.parse(JSON.stringify(this.followupReport));

    // 对于已确认的差异，如果用户修改了值，则应用用户的修改
    for (const item of this.diffItems) {
      if (item.confirmed) {
        // 解析路径，支持数组索引格式如 patients[0].name
        const pathParts = this.parsePath(item.path);
        
        // 找到目标位置
        let current = result;
        for (let i = 0; i < pathParts.length - 1; i++) {
          const part = pathParts[i];
          if (current[part] === undefined) {
            // 根据下一个部分决定创建对象还是数组
            const nextPart = pathParts[i + 1];
            current[part] = /^\d+$/.test(nextPart) ? [] : {};
          }
          current = current[part];
        }
        
        const lastPart = pathParts[pathParts.length - 1];
        
        // 应用用户修改的值
        if (item.modified) {
          current[lastPart] = item.userValue;
        } else if (item.confirmed) {
          // 如果确认但未修改，使用新值
          current[lastPart] = item.newValue;
        }
      }
    }

    return result;
  }

  // 解析路径，支持数组索引格式
  private parsePath(path: string): string[] {
    const parts: string[] = [];
    let current = '';
    let inBrackets = false;
    
    for (let i = 0; i < path.length; i++) {
      const char = path[i];
      
      if (char === '[') {
        if (current) {
          parts.push(current);
          current = '';
        }
        inBrackets = true;
      } else if (char === ']') {
        if (inBrackets) {
          parts.push(current);
          current = '';
          inBrackets = false;
        }
      } else if (char === '.' && !inBrackets) {
        if (current) {
          parts.push(current);
          current = '';
        }
      } else {
        current += char;
      }
    }
    
    if (current) {
      parts.push(current);
    }
    
    return parts;
  }

  // 进入下一步
  nextStep() {
    if (this.currentStep === DiffStep.PrimaryKeyConfig) {
      this.calculateDiff();
      this.currentStep = DiffStep.DiffConfirmation;
    } else if (this.currentStep === DiffStep.DiffConfirmation) {
      this.loadArgusData();
      this.currentStep = DiffStep.ArgusMapping;
    } else if (this.currentStep === DiffStep.ArgusMapping) {
      this.generateArgusReport();
      this.currentStep = DiffStep.ResultPreview;
    }
  }

  // 返回上一步
  prevStep() {
    if (this.currentStep === DiffStep.DiffConfirmation) {
      this.currentStep = DiffStep.PrimaryKeyConfig;
    } else if (this.currentStep === DiffStep.ArgusMapping) {
      this.currentStep = DiffStep.DiffConfirmation;
    } else if (this.currentStep === DiffStep.ResultPreview) {
      this.currentStep = DiffStep.ArgusMapping;
    }
  }

  // 设置加载状态
  setLoading(loading: boolean) {
    this.loading = loading;
  }

  // Argus报告相关
  argusData: any = null;
  argusMappings: Record<string, any> = {};
  argusReport: any = null;
  argusDiff: any[] = [];

  // 加载Argus数据
  async loadArgusData() {
    try {
      const response = await fetch('followup-diff/data/report-003.json');
      const data = await response.json();
      runInAction(() => {
        this.argusData = data;
      });
    } catch (error) {
      runInAction(() => {
        this.error = '加载Argus数据失败';
      });
    }
  }

  // 设置Argus映射
  setArgusMapping(sourcePath: string, mapping: any) {
    this.argusMappings[sourcePath] = mapping;
  }

  // 生成Argus报告
  generateArgusReport() {
    if (!this.argusData || !this.followupReport) return null;

    const newArgusReport = JSON.parse(JSON.stringify(this.argusData));
    
    // 中文到英文的字段映射
    const fieldMappings = {
      '受试者信息': {
        targetPath: 'reportSubjectInfo',
        fields: {
          '受试者中心号': 'center_no',
          '受试者筛选号': 'pat_subj_num',
          '受试者随机号': 'pat_rand_num',
          '受试者姓名缩写': 'pat_initials',
          '性别': 'TXT_pat_gender',
          '年龄': 'pat_age',
          '身高': 'pat_height',
          '体重': 'pat_weight',
          '出生日期': 'pat_dob',
          '民族': 'TXT_pat_ethnic_group_id'
        }
      },
      '与事件相关的现病史': {
        targetPath: 'reportSubjectInfo.rel_hist_add',
        fields: {
          '疾病名称': 'pat_hist_rptd',
          '开始日期': 'pat_hist_start',
          '结束日期': 'pat_hist_stop',
          '是否继续': 'pat_hist_cont'
        }
      },
      '与事件相关的既往病史': {
        targetPath: 'reportSubjectInfo.rel_hist_add',
        fields: {
          '疾病名称': 'pat_hist_rptd',
          '开始日期': 'pat_hist_start',
          '结束日期': 'pat_hist_stop',
          '是否继续': 'pat_hist_cont'
        }
      },
      '与事件相关的实验室检查': {
        targetPath: 'reportSubjectInfo.refExamineTable',
        fields: {
          '检查项目名称': 'labtestreptd',
          '检查结果': 'labnotes',
          '检查日期': 'examineDate'
        }
      }
    };

    // 应用映射
    Object.entries(fieldMappings).forEach(([sourceModule, config]) => {
      const sourceData = this.getValueByPath(this.followupReport, sourceModule);
      
      if (Array.isArray(sourceData)) {
        // 处理数组
        sourceData.forEach((item, index) => {
          const targetArray = this.getValueByPath(newArgusReport, config.targetPath) || [];
          const targetIndex = this.argusMappings[`${sourceModule}[${index}]`]?.targetIndex || index;
          
          if (targetArray[targetIndex]) {
            Object.entries(config.fields).forEach(([sourceField, targetField]) => {
              if (item[sourceField] !== undefined) {
                targetArray[targetIndex][targetField] = item[sourceField];
              }
            });
          }
        });
      } else if (sourceData && typeof sourceData === 'object') {
        // 处理对象
        const targetObj = this.getValueByPath(newArgusReport, config.targetPath) || {};
        Object.entries(config.fields).forEach(([sourceField, targetField]) => {
          if (sourceData[sourceField] !== undefined) {
            targetObj[targetField] = sourceData[sourceField];
          }
        });
      }
    });

    this.argusReport = newArgusReport;
    this.argusDiff = this.calculateArgusDiff();
    return newArgusReport;
  }

// 计算Argus差异
private calculateArgusDiff(): any[] {
  if (!this.argusData || !this.argusReport) return [];

  const differences: any[] = [];

  const compareObjects = (oldObj: any, newObj: any, path: string = '') => {
    // 处理基本类型或null值
    if (typeof oldObj !== 'object' || typeof newObj !== 'object' || oldObj === null || newObj === null) {
      if (JSON.stringify(oldObj) !== JSON.stringify(newObj)) {
        differences.push({
          path,
          oldValue: oldObj,
          newValue: newObj,
          type: 'modified',
          description: `字段 ${path} 的值从 "${oldObj}" 修改为 "${newObj}"`
        });
      }
      return;
    }

    // 处理数组
    if (Array.isArray(oldObj) || Array.isArray(newObj)) {
      const oldArray = Array.isArray(oldObj) ? oldObj : [];
      const newArray = Array.isArray(newObj) ? newObj : [];

      const maxLength = Math.max(oldArray.length, newArray.length);

      for (let i = 0; i < maxLength; i++) {
        const currentPath = `${path}[${i}]`;
        const oldItem = oldArray[i];
        const newItem = newArray[i];

        if (oldItem === undefined && newItem !== undefined) {
          differences.push({
            path: currentPath,
            oldValue: undefined,
            newValue: newItem,
            type: 'added',
            description: `在 ${path} 数组中新增了第 ${i + 1} 项`
          });
        } else if (oldItem !== undefined && newItem === undefined) {
          differences.push({
            path: currentPath,
            oldValue: oldItem,
            newValue: undefined,
            type: 'deleted',
            description: `从 ${path} 数组中删除了第 ${i + 1} 项`
          });
        } else if (JSON.stringify(oldItem) !== JSON.stringify(newItem)) {
          compareObjects(oldItem, newItem, currentPath);
        }
      }
      return;
    }

    // 处理对象
    const allKeys = new Set([...Object.keys(oldObj || {}), ...Object.keys(newObj || {})]);

    for (const key of Array.from(allKeys)) {
      const currentPath = path ? `${path}.${key}` : key;
      const oldVal = oldObj?.[key];
      const newVal = newObj?.[key];

      if (oldVal === undefined && newVal !== undefined) {
        differences.push({
          path: currentPath,
          oldValue: undefined,
          newValue: newVal,
          type: 'added',
          description: `新增字段 ${currentPath}`
        });
      } else if (oldVal !== undefined && newVal === undefined) {
        differences.push({
          path: currentPath,
          oldValue: oldVal,
          newValue: undefined,
          type: 'deleted',
          description: `删除字段 ${currentPath}`
        });
      } else if (JSON.stringify(oldVal) !== JSON.stringify(newVal)) {
        compareObjects(oldVal, newVal, currentPath);
      }
    }
  };

  compareObjects(this.argusData, this.argusReport);
  return differences;
}

  // 重置状态
  reset() {
    this.currentStep = DiffStep.PrimaryKeyConfig;
    this.diffResult = null;
    this.diffItems = [];
    this.error = null;
    this.argusData = null;
    this.argusMappings = {};
    this.argusReport = null;
    this.argusDiff = [];
  }
}

export default DiffStore;
