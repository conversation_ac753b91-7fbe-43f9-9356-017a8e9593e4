import React, { useState, useMemo } from "react";
import { SchemaFormContext, SchemaFormProps } from "./SchemaFormContext";
import { ValueRenderer } from "./ValueRenderer";
import { ObjectRenderer } from "./ObjectRenderer";
import { ListRenderer } from "./ListRenderer";
import { TableRenderer } from "./TableRenderer";

export const SchemaForm: React.FC<SchemaFormProps> = ({ data, readonly = true, onChange }) => {
  const [activeSection, setActiveSection] = useState<string | null>(null);

  const contextValue = useMemo(() => ({
    data,
    readonly,
    onChange
  }), [data, readonly, onChange]);

  const topLevelKeys = useMemo(() => {
    if (typeof data === "object" && data !== null && !Array.isArray(data)) {
      return Object.keys(data).filter(key => key !== '报告分类' && key !== '报告模块');
    }
    return [];
  }, [data]);

  const handleNavClick = (key: string) => {
    const element = document.getElementById(`section-${key}`);
    if (element) {
      element.scrollIntoView({ behavior: "smooth", block: "start" });
      setActiveSection(key);
    }
  };

  return (
    <SchemaFormContext.Provider value={contextValue}>
      <div className="w-full">
      {topLevelKeys.length > 0 && (
        <nav className="print:hidden sticky top-0 z-10 bg-white/80 backdrop-blur-md shadow-sm border-b border-gray-200 px-3 py-1.5 flex items-center">
          <ul className="flex flex-wrap gap-x-2 gap-y-1 items-center justify-start flex-grow">
            {topLevelKeys.map((key) => (
              <li key={key}> 
                <button
                  onClick={() => handleNavClick(key)}
                  className={`px-3 py-1.5 rounded text-xs font-medium transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-blue-500 ${
                    activeSection === key
                      ? "bg-blue-500 text-white shadow-md transform scale-100"   
                      : "text-gray-600 hover:bg-gray-100 hover:text-gray-800"
                  }`}
                >
                  {key} {Array.isArray(data[key]) ? <span className="text-red-600">({data[key].length})</span> : null}
                </button>
              </li>
            ))}
          </ul>
        </nav>
      )}
      {typeof data === "object" && data !== null ? (
        Array.isArray(data) ? (
          data.length > 0 && typeof data[0] !== "object" ? (
            <ListRenderer path='$.' data={data} />
          ) : (
            <TableRenderer path='$.' data={data} />
          )
        ) : (
          <ObjectRenderer path='$.' data={data} topLevelKeysForNav={topLevelKeys} />
        )
      ) : (
        <ValueRenderer path='$.' value={data} />
      )}
      </div>
    </SchemaFormContext.Provider>
  );
};