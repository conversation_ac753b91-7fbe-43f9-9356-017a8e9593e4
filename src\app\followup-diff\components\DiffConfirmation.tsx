import React, { useState } from 'react';
import { observer } from 'mobx-react-lite';
import DiffStore, { DiffItem } from '../stores/diffStore';

interface DiffConfirmationProps {
  store: DiffStore;
}

const DiffConfirmation: React.FC<DiffConfirmationProps> = observer(({ store }) => {
  const [editingItem, setEditingItem] = useState<string | null>(null);
  const [editValue, setEditValue] = useState<string>('');

  // 开始编辑
  const startEdit = (item: DiffItem) => {
    setEditingItem(item.path);
    setEditValue(typeof item.userValue === 'string' ? item.userValue : JSON.stringify(item.userValue));
  };

  // 保存编辑
  const saveEdit = (path: string) => {
    try {
      let value: any = editValue;
      // 尝试解析为JSON，如果失败则保持字符串
      try {
        value = JSON.parse(editValue);
      } catch {
        // 保持字符串值
      }
      
      store.modifyDiffItem(path, value);
      setEditingItem(null);
      setEditValue('');
    } catch (error) {
      alert('保存失败，请检查输入格式');
    }
  };

  // 取消编辑
  const cancelEdit = () => {
    setEditingItem(null);
    setEditValue('');
  };

  // 获取字段的中文标签
  const getFieldLabel = (fieldName: string): string => {
    const fieldMap: Record<string, string> = {
      '报告分类': '报告分类',
      'center_no': '中心号',
      'pat_subj_num': '受试者筛选号',
      'pat_rand_num': '受试者随机号',
      'pat_initials': '姓名缩写',
      'TXT_pat_gender': '性别',
      'pat_age': '年龄',
      'pat_height': '身高',
      'pat_weight': '体重',
      'pat_dob': '出生日期',
      'TXT_pat_ethnic_group_id': '民族',
      'pat_hist_rptd': '疾病名称',
      'pat_hist_start': '开始日期',
      'pat_hist_stop': '结束日期',
      'pat_hist_cont': '是否继续',
      'labtestreptd': '检查项目名称',
      'labnotes': '检查结果',
      'examineDate': '检查日期'
    };
    
    return fieldMap[fieldName] || fieldName;
  };

  // 格式化字段值显示
  const formatFieldValue = (value: any): string => {
    if (value === undefined || value === null) return '(空)';
    if (value === '') return '(空字符串)';
    if (Array.isArray(value) && value.length === 0) return '(空列表)';
    if (typeof value === 'object') {
      return JSON.stringify(value, null, 2);
    }
    return String(value);
  };

  // 获取变更类型
  const getChangeType = (item: DiffItem): 'modified' | 'added' => {
    return item.oldValue === undefined ? 'added' : 'modified';
  };

  // 获取变更类型的样式
  const getChangeTypeStyle = (type: 'modified' | 'added') => {
    switch (type) {
      case 'added':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'modified':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // 获取变更类型的标签
  const getChangeTypeLabel = (type: 'modified' | 'added') => {
    switch (type) {
      case 'added':
        return '新增';
      case 'modified':
        return '修改';
      default:
        return '未知';
    }
  };

  // 解析路径显示
  const parsePathDisplay = (path: string) => {
    // 处理数组路径格式如 patients[0].name
    const arrayMatch = path.match(/^(.+)\[(\d+)\]\.(.+)$/);
    if (arrayMatch) {
      const [, arrayName, index, field] = arrayMatch;
      return {
        type: 'array_field',
        arrayName: getFieldLabel(arrayName),
        index: parseInt(index),
        field: getFieldLabel(field)
      };
    }
    
    // 处理普通对象路径
    const parts = path.split('.');
    const field = parts[parts.length - 1];
    const parentPath = parts.slice(0, -1).join('.');
    
    return {
      type: 'field',
      parentPath: parentPath || '根对象',
      field: getFieldLabel(field)
    };
  };

  if (store.loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">计算差异中...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="text-lg font-medium text-blue-900 mb-2">步骤 2: 差异确认</h3>
        <p className="text-blue-700">
          检查并确认检测到的差异。您可以修改值或选择是否应用每个变更。
        </p>
      </div>

      {/* 统计信息 */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-gray-900">{store.diffItems.length}</div>
            <div className="text-sm text-gray-600">总差异数</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-600">
              {store.diffItems.filter(item => getChangeType(item) === 'added').length}
            </div>
            <div className="text-sm text-gray-600">新增项</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-blue-600">
              {store.diffItems.filter(item => getChangeType(item) === 'modified').length}
            </div>
            <div className="text-sm text-gray-600">修改项</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-purple-600">
              {store.diffItems.filter(item => item.confirmed).length}
            </div>
            <div className="text-sm text-gray-600">已确认</div>
          </div>
        </div>
      </div>

      {/* 批量操作 */}
      <div className="flex gap-2">
        <button
          onClick={() => store.diffItems.forEach(item => store.confirmDiffItem(item.path, true))}
          className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 text-sm"
        >
          全部确认
        </button>
        <button
          onClick={() => store.diffItems.forEach(item => store.confirmDiffItem(item.path, false))}
          className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 text-sm"
        >
          全部取消
        </button>
      </div>

      {/* 差异列表 */}
      {store.diffItems.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <p>未检测到差异</p>
        </div>
      ) : (
        <div className="space-y-4">
          {store.diffItems.map((item) => {
            const changeType = getChangeType(item);
            const isEditing = editingItem === item.path;
            const pathInfo = parsePathDisplay(item.path);
            
            return (
              <div
                key={item.path}
                className={`border rounded-lg p-4 ${
                  item.confirmed ? 'border-green-300 bg-green-50' : 'border-gray-200'
                }`}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <span className={`px-2 py-1 rounded text-xs font-medium border ${getChangeTypeStyle(changeType)}`}>
                      {getChangeTypeLabel(changeType)}
                    </span>
                    <div className="text-sm">
                      {pathInfo.type === 'array_field' ? (
                        <span>
                          {pathInfo.arrayName}[{pathInfo.index}].{pathInfo.field}
                        </span>
                      ) : (
                        <span>
                          {pathInfo.parentPath}.{pathInfo.field}
                        </span>
                      )}
                    </div>
                    {item.modified && (
                      <span className="px-2 py-1 rounded text-xs font-medium bg-purple-100 text-purple-800 border border-purple-200">
                        已修改
                      </span>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={item.confirmed}
                        onChange={(e) => store.confirmDiffItem(item.path, e.target.checked)}
                        className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">确认</span>
                    </label>
                  </div>
                </div>

                <div className="space-y-3">
                  {changeType === 'modified' ? (
                    <>
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                        {/* 原值 */}
                        <div className="bg-red-50 border border-red-200 rounded p-3">
                          <div className="flex items-center justify-between mb-2">
                            <span className="bg-red-100 text-red-800 text-xs font-medium px-2 py-1 rounded">
                              原值
                            </span>
                          </div>
                          <div className="text-red-700 text-sm">
                            {formatFieldValue(item.oldValue)}
                          </div>
                        </div>
                        
                        {/* 新值 */}
                        <div className="bg-green-50 border border-green-200 rounded p-3">
                          <div className="flex items-center justify-between mb-2">
                            <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded">
                              新值
                            </span>
                            <button
                              onClick={() => startEdit(item)}
                              className="text-blue-600 hover:text-blue-800 text-xs"
                            >
                              编辑
                            </button>
                          </div>
                          
                          {isEditing ? (
                            <div className="space-y-2">
                              <textarea
                                value={editValue}
                                onChange={(e) => setEditValue(e.target.value)}
                                className="w-full p-2 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500"
                                rows={2}
                                placeholder="输入新值..."
                              />
                              <div className="flex gap-1">
                                <button
                                  onClick={() => saveEdit(item.path)}
                                  className="bg-green-600 text-white px-2 py-1 rounded text-xs hover:bg-green-700"
                                >
                                  保存
                                </button>
                                <button
                                  onClick={cancelEdit}
                                  className="bg-gray-600 text-white px-2 py-1 rounded text-xs hover:bg-gray-700"
                                >
                                  取消
                                </button>
                              </div>
                            </div>
                          ) : (
                            <div className="text-green-700 text-sm">
                              {formatFieldValue(item.modified ? item.userValue : item.newValue)}
                            </div>
                          )}
                        </div>
                      </div>
                    </>
                  ) : (
                    <div>
                      <div className="bg-green-50 border border-green-200 rounded p-3">
                        <div className="flex items-center justify-between mb-2">
                          <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded">
                            新增值
                          </span>
                          <button
                            onClick={() => startEdit(item)}
                            className="text-blue-600 hover:text-blue-800 text-xs"
                          >
                            编辑
                          </button>
                        </div>
                        
                        {isEditing ? (
                          <div className="space-y-2">
                            <textarea
                              value={editValue}
                              onChange={(e) => setEditValue(e.target.value)}
                              className="w-full p-2 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500"
                              rows={2}
                              placeholder="输入值..."
                            />
                            <div className="flex gap-1">
                              <button
                                onClick={() => saveEdit(item.path)}
                                className="bg-green-600 text-white px-2 py-1 rounded text-xs hover:bg-green-700"
                              >
                                保存
                              </button>
                              <button
                                onClick={cancelEdit}
                                className="bg-gray-600 text-white px-2 py-1 rounded text-xs hover:bg-gray-700"
                              >
                                取消
                              </button>
                            </div>
                          </div>
                        ) : (
                          <div className="text-green-700 text-sm">
                            {formatFieldValue(item.modified ? item.userValue : item.newValue)}
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* 导航按钮 */}
      <div className="flex justify-between pt-6 border-t border-gray-200">
        <button
          onClick={() => store.prevStep()}
          className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700"
        >
          上一步
        </button>
        <button
          onClick={() => store.nextStep()}
          disabled={store.diffItems.filter(item => item.confirmed).length === 0}
          className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          下一步：预览结果
        </button>
      </div>
    </div>
  );
});

export default DiffConfirmation;
