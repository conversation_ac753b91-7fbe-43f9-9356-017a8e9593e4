import React, { useState, useEffect } from 'react';
import { BiErrorCircle } from 'react-icons/bi';

// 定义API表格的数据类型
interface ApiTableData {
  headers: string[];
  rows: string[][];
  loading?: boolean;
  error?: string;
}

// API表格组件
interface ApiTableProps {
  apiUrl: string;
  className?: string;
}

const ApiTable: React.FC<ApiTableProps> = ({ apiUrl, className = '' }) => {
  const [data, setData] = useState<ApiTableData>({
    headers: [],
    rows: [],
    loading: true,
    error: undefined
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        setData(prev => ({ ...prev, loading: true, error: undefined }));
        
        const response = await fetch(apiUrl);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        
        // 假设API返回的数据格式为 { headers: string[], rows: string[][] }
        // 或者 { data: Array<Object> } 格式
        let headers: string[] = [];
        let rows: string[][] = [];
        
        if (result.headers && result.rows) {
          // 直接的表格格式
          headers = result.headers;
          rows = result.rows;
        } else if (result.data && Array.isArray(result.data) && result.data.length > 0) {
          // 对象数组格式，提取第一个对象的键作为表头
          headers = Object.keys(result.data[0]);
          rows = result.data.map((item: any) => 
            headers.map(header => String(item[header] || ''))
          );
        } else if (Array.isArray(result) && result.length > 0) {
          // 直接是对象数组
          headers = Object.keys(result[0]);
          rows = result.map((item: any) => 
            headers.map(header => String(item[header] || ''))
          );
        } else {
          throw new Error('不支持的数据格式');
        }
        
        setData({ headers, rows, loading: false });
      } catch (error) {
        console.error('获取API数据失败:', error);
        setData(prev => ({
          ...prev,
          loading: false,
          error: error instanceof Error ? error.message : '未知错误'
        }));
      }
    };

    if (apiUrl) {
      fetchData();
    }
  }, [apiUrl]);

  if (data.loading) {
    return (
      <div className={`api-table-loading ${className}`}>
        <div className="flex items-center justify-center p-8 bg-gray-50 rounded-lg">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
          <span className="text-gray-600">加载数据中...</span>
        </div>
      </div>
    );
  }

  if (data.error) {
    return (
      <div className={`api-table-error ${className}`}>
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center">
            <BiErrorCircle className="text-red-500 text-xl mr-2" />
            <div>
              <p className="text-red-800 font-medium">数据加载失败</p>
              <p className="text-red-600 text-sm mt-1">{data.error}</p>
              <p className="text-red-600 text-sm">API地址: {apiUrl}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (data.headers.length === 0) {
    return (
      <div className={`api-table-empty ${className}`}>
        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800">没有可显示的数据</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`api-table ${className}`}>
      <div className="overflow-x-auto">
        <table className="min-w-full border-collapse border border-gray-300 rounded-lg overflow-hidden">
          <thead className="bg-gray-50">
            <tr>
              {data.headers.map((header, index) => (
                <th
                  key={index}
                  className="border border-gray-300 px-4 py-2 text-left font-semibold text-gray-900"
                >
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {data.rows.map((row, rowIndex) => (
              <tr
                key={rowIndex}
                className={rowIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50'}
              >
                {row.map((cell, cellIndex) => (
                  <td
                    key={cellIndex}
                    className="border border-gray-300 px-4 py-2 text-gray-700"
                  >
                    {cell}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      <div className="mt-2 text-xs text-gray-500 text-right">
        数据来源: {apiUrl} | 共 {data.rows.length} 条记录
      </div>
    </div>
  );
};

// 处理API表格语法的函数
export const processApiTables = (content: string): { content: string; components: Map<string, React.ComponentType> } => {
  const components = new Map<string, React.ComponentType>();
  let processedContent = content;

  // 匹配 ```api-table:url``` 语法
  const apiTableRegex = /```api-table:([^\n]+)\n?([^`]*)```/g;
  let match;
  let componentCounter = 0;

  while ((match = apiTableRegex.exec(content)) !== null) {
    const [fullMatch, apiUrl, options = ''] = match;
    const componentId = `api-table-${componentCounter++}`;
    
    // 解析选项（如果有的话）
    const parsedOptions: any = {};
    if (options.trim()) {
      try {
        const optionLines = options.trim().split('\n');
        optionLines.forEach(line => {
          const [key, value] = line.split(':').map(s => s.trim());
          if (key && value) {
            parsedOptions[key] = value;
          }
        });
      } catch (e) {
        console.warn('解析API表格选项失败:', e);
      }
    }

    // 创建组件
    const ApiTableComponent = () => (
      <ApiTable 
        apiUrl={apiUrl.trim()} 
        className={parsedOptions.className || 'my-4'}
      />
    );

    components.set(componentId, ApiTableComponent);
    
    // 替换为占位符
    processedContent = processedContent.replace(fullMatch, `<div id="${componentId}"></div>`);
  }

  return { content: processedContent, components };
};

export default ApiTable;