import React, { useEffect, useState } from "react";
import _ from 'lodash';
import { useSchemaForm } from "./SchemaFormContext";
import { EditModal } from "./EditModal";

interface ValueRendererProps {
  path: string;
  value?: any;
}

export const ValueRenderer: React.FC<ValueRendererProps> = ({ path, value }) => {
  const { data, onChange, readonly } = useSchemaForm();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [displayValue, setDisplayValue] = useState(value?.toString() || '');
  const [editValue, setEditValue] = useState('');

  useEffect(() => {
    setDisplayValue(value?.toString() || '');
  },[value])

  if (value === null || value === undefined) {
    return <div data-path={path} className="text-gray-400">-</div>;
  }

  const handleDoubleClick = () => {
    if (readonly) return;
    setIsModalOpen(true);
    // 打开弹窗时，将编辑值设为当前显示值
    setEditValue(displayValue);
  };

  const handleInput = (newValue: string) => {
    // 仅更新编辑框中的值，不影响显示值
    setEditValue(newValue);
  };

  const handleSave = () => {
    // 只有在保存时才更新显示值和数据
    setDisplayValue(editValue);
    console.log('editValue :>> ', path, editValue);
    _.set(data, path.replace('$.',''), editValue);
    if(onChange) {
      onChange(data, path, editValue);
    }
    setIsModalOpen(false);
  };

  const handleClose = () => {
    // 取消时不更新任何值
    setIsModalOpen(false);
  };

  const fieldName = path.split('.').pop() || '';

  return (
    <>
      <div
        data-path={path}
        className={`text-gray-700 ${!readonly ? "cursor-pointer hover:bg-gray-100" : ""}`}
        onDoubleClick={handleDoubleClick}
        title={!readonly ? "双击编辑" : undefined}
      >
        {displayValue.toString().length > 0 ? displayValue.toString() : '-'}
      </div>
      
      <EditModal
        isOpen={isModalOpen}
        onClose={handleClose}
        onSave={handleSave}
        value={editValue}
        onChange={handleInput}
        title={`编辑: ${fieldName}`}
      />
    </>
  );
};
