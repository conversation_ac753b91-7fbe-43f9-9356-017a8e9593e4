# 租户初始化页面
 

### 租户列表

```api-table:/kb-server/tenant/tenantList
{
  "buttons": [
    {
      "id": "edit-tenant",
      "text": "编辑租户",
      "description": "编辑租户信息",
      "type": "row",
      "variant": "primary",
      "method": "POST",
      "url": "/kb-server/tenant/tenantUpdate",
      "rowDataMapping": {
        "id": "id",
        "tenantId": "tenantId",
        "tenantName": "tenantName"
      },
      "params": [
        {
          "name": "id",
          "type": "string",
          "required": true,
          "description": "ID"
        },
        {
          "name": "tenantId",
          "type": "string",
          "required": true,
          "description": "tenantId"
        },
        {
          "name": "tenantName",
          "type": "string",
          "required": true,
          "description": "tenantName"
        }
      ]
    }
  ]
}
```
### 租户绑定列表

```api-table:/kb-server/tenant/tenantRelList
{
  "buttons": [
    {
      "id": "edit-tenant",
      "text": "注册租户信息",
      "description": "注册租户信息",
      "type": "row",
      "variant": "primary",
      "method": "POST",
      "url": "/kb-server/tenant/tenantAppRegister",
      "rowDataMapping": {
        "tenantAppRelId": "tenantAppRelId",
        "tenantId": "tenantId",
        "tenantName": "tenantName"
      },
      "params": [
        {
          "name": "tenantAppRelId",
          "type": "string",
          "required": true,
          "description": "ID"
        },
        {
          "name": "tenantId",
          "type": "string",
          "required": true,
          "description": "tenantId"
        },
        {
          "name": "tenantName",
          "type": "string",
          "required": true,
          "description": "tenantName"
        },
        {
          "name": "sourceTenantId",
          "type": "string",
          "required": false,
          "defaultValue": "lanovamedicines",
          "description": "sourceTenantId"
        },
        {
          "name": "sourceCompanyName",
          "type": "string",
          "required": false,
          "defaultValue": "礼新医药",
          "description": "sourceCompanyName"
        },{
          "name": "language",
          "type": "enum",
          "enumOptions": ["cn", "en"],
          "required": false,
          "defaultValue": "cn",
          "description": "language"
        }
      ]
    }
  ]
}
```


### 从Excel导入字典标准数据

```api-caller:{
  "id": "dict-importExcel",
  "name": "从Excel导入字典标准数据",
  "description": "从Excel导入字典标准数据",
  "method": "POST",
  "url": "/kb-server/dict/standard/importExcel",
  "params": [
    {
      "name": "file",
      "type": "file",
      "required": true
    }
  ]
}
```


### 从Excel导入字典数据 
```api-caller:{
  "id": "dict-import",
  "name": "从Excel导入字典数据",
  "description": "从Excel导入字典数据",
  "method": "POST",
  "url": "/api/pv/dict/import",
  "streaming": true,
  "params": [
    {
      "name": "file",
      "type": "file",
      "required": true
    },
    {
      "name": "company_code",      
      "type": "enum",
      "required": true,
      "enumOptions": ["kelun", "AVB"],
      "description": "公司代码,AVB就是康龙"
    },
    {
      "name": "whodrug_table_name",
      "type": "dict",
      "dictUrl": "/kb-server/api/pmc/tables",
      "required": true,
      "description": "WHODrug表名，必须传入"
    },
    {
      "name": "meddra_table_name",
      "type": "dict",
      "dictUrl": "/kb-server/api/pmc/tables",
      "required": true,
      "description": "MedDRA表名，必须传入"
    }
  ]
}
``` 


### 导入研究方案Excel文件  
```api-caller:{
  "id": "study-import",
  "name": "导入研究方案Excel文件",
  "description": "导入研究方案Excel文件",
  "method": "POST",
  "url": "/api/pv/study-info/import-excel",
  "params": [
    {
      "name": "file",
      "type": "file",
      "required": true,
      "description": "Excel文件"
    },
    {
      "name": "import_study_info",
      "type": "boolean",
      "required": true,
      "description": "是否导入study_info表数据"
    },
    {
      "name": "import_pat_exposure",
      "type": "boolean",
      "required": true,
      "description": "是否导入pat_exposure字典数据(dictionary_mapping表)"
    },
    {
      "name": "template_formats",
      "type": "string",
      "required": true,
      "description": "JSON格式的方案编号模板格式配置"
    }
  ]
}
```

### 分页查询试验方案信息

```api-table:/kb-server/api/pms/study/page
```
### 分页查询字典映射信息

```api-table:/kb-server/api/pms/dictionary/page
``` 

### PMC分页查询表数据

```api-table:/kb-server/api/pmc/query
{
  "pageSize": 10,
  "showSearch": true,
  "showPagination": true, 
  "queryParams": [
    {
      "name": "tableName",
      "label": "tableName",
      "type": "dict",
      "dictUrl": "/kb-server/api/pmc/tables"
    }
  ]
}
```

### 分页查询字典标准数据

```api-table:/kb-server/api/kb/pageDictStandard
```


### 分页查询转换配置数据

```api-table:/kb-server/api/kb/transform/page
{
  "buttons": [
    {
      "id": "add-transform",
      "text": "新增",
      "description": "新增转换配置",
      "type": "header",
      "variant": "primary",
      "method": "POST",
      "url": "/kb-server/api/kb/transform/add",
      "params": [
        {
          "name": "tenantId",
          "type": "string",
          "required": true,
          "description": "tenantId"
        },
        {
          "name": "appId",
          "type": "string",
          "required": true,
          "description": "appId"
        },
        {
          "name": "originJsonPath",
          "type": "string",
          "required": true,
          "description": "originJsonPath"
        },
        {
          "name": "jqScript",
          "type": "string",
          "required": true,
          "description": "jqScript"
        },
        {
          "name": "resultType",
          "type": "enum",
          "required": true,
          "enumOptions": ["xml", "json"],
          "description": "resultType"
        },
        {
          "name": "status",
          "type": "enum",
          "required": true,
          "enumOptions": ["active", "deprecated"],
          "description": "status"
        },
        {
          "name": "skipPostProcessing",
          "type": "number",
          "required": true,
          "defaultValue": 1,
          "description": "skipPostProcessing"
        }
      ]
    },
    {
      "id": "edit-transform",
      "text": "编辑",
      "description": "编辑转换配置",
      "type": "row",
      "variant": "primary",
      "method": "POST",
      "url": "/kb-server/api/kb/transform/update",
      "rowDataMapping": {
        "id": "id",
        "jqScript": "jqScript"
      },
      "params": [
        {
          "name": "id",
          "type": "string",
          "required": true,
          "description": "ID"
        },
        {
          "name": "jqScript",
          "type": "string",
          "required": true,
          "description": "jqScript"
        }
      ]
    },
    {
      "id": "del-transform",
      "text": "删除",
      "description": "删除转换配置",
      "type": "row",
      "variant": "primary",
      "method": "POST",
      "url": "/kb-server/api/kb/transform/delete",
      "rowDataMapping": {
        "id": "id"
      },
      "params": [
        {
          "name": "id",
          "type": "string",
          "required": true,
          "description": "ID"
        }
      ]
    }
  ]
}
```


### 分页查询租户Meddra版本数据

```api-table:/kb-server/api/kb/meddra/page
{
  "buttons": [
    {
      "id": "add-meddra",
      "text": "新增",
      "description": "新增meddra",
      "type": "header",
      "variant": "primary",
      "method": "POST",
      "url": "/kb-server/api/kb/meddra/add",
      "params": [
        {
          "name": "tenantId",
          "type": "string",
          "required": true,
          "description": "tenantId"
        },
        {
          "name": "appId",
          "type": "string",
          "required": true,
          "description": "appId"
        },
        {
          "name": "versionId",
          "type": "string",
          "required": true,
          "description": "versionId"
        },
        {
          "name": "versionName",
          "type": "string",
          "required": true,
          "description": "versionName"
        },  
        {
          "name": "languageInfo",
          "type": "string",
          "required": true,
          "description": "languageInfo"
        },
        {
          "name": "tableName",
          "type": "string",
          "required": true,
          "description": "tableName"
        }
      ]
    },
    {
      "id": "edit-meddra",
      "text": "编辑",
      "description": "编辑meddra",
      "type": "row",
      "variant": "primary",
      "method": "POST",
      "url": "/kb-server/api/kb/meddra/update",
      "rowDataMapping": {
        "id": "id",
        "tableName": "tableName"
      },
      "params": [
        {
          "name": "id",
          "type": "string",
          "required": true,
          "description": "ID"
        },
        {
          "name": "tableName",
          "type": "string",
          "required": true,
          "description": "jqScript"
        }
      ]
    },
    {
      "id": "del-meddra",
      "text": "删除",
      "description": "删除meddra",
      "type": "row",
      "variant": "primary",
      "method": "POST",
      "url": "/kb-server/api/kb/meddra/delete",
      "rowDataMapping": {
        "id": "id"
      },
      "params": [
        {
          "name": "id",
          "type": "string",
          "required": true,
          "description": "ID"
        }
      ]
    }
  ]
}
```



### 分页查询租户Whodrug版本数据

```api-table:/kb-server/api/kb/whodrug/page
{
  "buttons": [
    {
      "id": "add-whodrug",
      "text": "新增",
      "description": "新增whodrug",
      "type": "header",
      "variant": "primary",
      "method": "POST",
      "url": "/kb-server/api/kb/whodrug/add",
      "params": [
        {
          "name": "tenantId",
          "type": "string",
          "required": true,
          "description": "tenantId"
        },
        {
          "name": "appId",
          "type": "string",
          "required": true,
          "description": "appId"
        },
        {
          "name": "tableName",
          "type": "string",
          "required": true,
          "description": "tableName"
        }
      ]
    },
    {
      "id": "edit-whodrug",
      "text": "编辑",
      "description": "编辑whodrug",
      "type": "row",
      "variant": "primary",
      "method": "POST",
      "url": "/kb-server/api/kb/whodrug/update",
      "rowDataMapping": {
        "id": "id",
        "tableName": "tableName"
      },
      "params": [
        {
          "name": "id",
          "type": "string",
          "required": true,
          "description": "ID"
        },
        {
          "name": "tableName",
          "type": "string",
          "required": true,
          "description": "jqScript"
        }
      ]
    },
    {
      "id": "del-whodrug",
      "text": "删除",
      "description": "删除whodrug",
      "type": "row",
      "variant": "primary",
      "method": "POST",
      "url": "/kb-server/api/kb/whodrug/delete",
      "rowDataMapping": {
        "id": "id"
      },
      "params": [
        {
          "name": "id",
          "type": "string",
          "required": true,
          "description": "ID"
        }
      ]
    }
  ]
}
```