import React, { useState, useEffect, useCallback } from 'react';
import { observer } from 'mobx-react-lite';
import DiffStore from '../stores/diffStore';

interface ArgusMappingProps {
  store: DiffStore;
}

// 中文到英文的字段映射配置
const fieldMappings = {
  '受试者信息': {
    targetPath: 'reportSubjectInfo',
    fields: {
      '受试者中心号': 'center_no',
      '受试者筛选号': 'pat_subj_num',
      '受试者随机号': 'pat_rand_num',
      '受试者姓名缩写': 'pat_initials',
      '性别': 'TXT_pat_gender',
      '年龄': 'pat_age',
      '身高': 'pat_height',
      '体重': 'pat_weight',
      '出生日期': 'pat_dob',
      '民族': 'TXT_pat_ethnic_group_id',
      '种族': 'pat_race_add'
    }
  },
  '与事件相关的现病史': {
    targetPath: 'reportSubjectInfo.rel_hist_add',
    fields: {
      '疾病名称': 'pat_hist_rptd',
      '开始日期': 'pat_hist_start',
      '结束日期': 'pat_hist_stop',
      '是否继续': 'pat_hist_cont'
    }
  },
  '与事件相关的既往病史': {
    targetPath: 'reportSubjectInfo.rel_hist_add',
    fields: {
      '疾病名称': 'pat_hist_rptd',
      '开始日期': 'pat_hist_start',
      '结束日期': 'pat_hist_stop',
      '是否继续': 'pat_hist_cont'
    }
  },
  '与事件相关的实验室检查': {
    targetPath: 'reportSubjectInfo.refExamineTable',
    fields: {
      '检查项目名称': 'labtestreptd',
      '检查结果': 'labnotes',
      '检查日期': 'examineDate'
    }
  }
};

const ArgusMapping: React.FC<ArgusMappingProps> = observer(({ store }) => {
  const [selectedMappings, setSelectedMappings] = useState<Record<string, any>>({});

  // 解析数组路径，提取模块名、索引和字段名
  const parseArrayPath = (path: string) => {
    // 匹配格式如: "与事件相关的现病史[0].疾病名称"
    const arrayMatch = path.match(/^(.+?)\[(\d+)\](?:\.(.+))?$/);
    if (arrayMatch) {
      return {
        module: arrayMatch[1],
        index: parseInt(arrayMatch[2]),
        field: arrayMatch[3] || null
      };
    }
    return null;
  };

  // 根据diffItems初始化映射关系
  const initializeMappingsFromDiff = useCallback(() => {
    const newMappings: Record<string, any> = {};

    // 遍历diffItems，为数组项建立映射关系
    store.diffItems.forEach(item => {
      const pathInfo = parseArrayPath(item.path);
      if (pathInfo && fieldMappings[pathInfo.module as keyof typeof fieldMappings]) {
        const moduleKey = pathInfo.module;
        const sourceIndex = pathInfo.index;

        // 如果是修改项（有oldValue），映射到对应的目标位置
        if (item.oldValue !== undefined) {
          const mappingKey = `${moduleKey}[${sourceIndex}]`;
          newMappings[mappingKey] = {
            targetPath: fieldMappings[moduleKey as keyof typeof fieldMappings].targetPath,
            targetIndex: sourceIndex // 修改项映射到相同索引位置
          };

          // 同时在store中设置映射
          store.setArgusMapping(mappingKey, {
            targetPath: fieldMappings[moduleKey as keyof typeof fieldMappings].targetPath,
            targetIndex: sourceIndex
          });
        }
      }
    });

    setSelectedMappings(prev => ({ ...prev, ...newMappings }));
  }, [store]);

  useEffect(() => {
    store.loadArgusData();
  }, [store]);

  // 根据差异结果初始化映射关系
  useEffect(() => {
    if (store.argusData && store.diffItems.length > 0) {
      initializeMappingsFromDiff();
    }
  }, [store.argusData, store.diffItems, initializeMappingsFromDiff]);

  // 获取当前报告中的数据
  const getCurrentReportData = (moduleKey: string) => {
    switch (moduleKey) {
      case '受试者信息':
        return store.followupReport?.['受试者信息'] || {};
      case '与事件相关的现病史':
        return store.followupReport?.['与事件相关的现病史'] || [];
      case '与事件相关的既往病史':
        return store.followupReport?.['与事件相关的既往病史'] || [];
      case '与事件相关的实验室检查':
        return store.followupReport?.['与事件相关的实验室检查'] || [];
      default:
        return {};
    }
  };

  // 处理字段映射
  const handleFieldMapping = (moduleKey: string, sourceField: string, targetField: string, value: any) => {
    const newMappings = {
      ...selectedMappings,
      [`${moduleKey}.${sourceField}`]: {
        targetPath: fieldMappings[moduleKey as keyof typeof fieldMappings].targetPath,
        targetField,
        value
      }
    };
    setSelectedMappings(newMappings);
    store.setArgusMapping(`${moduleKey}.${sourceField}`, {
      targetPath: fieldMappings[moduleKey as keyof typeof fieldMappings].targetPath,
      targetField,
      value
    });
  };

  // 处理数组项映射
  const handleArrayItemMapping = (moduleKey: string, sourceIndex: number, targetIndex: number) => {
    const newMappings = {
      ...selectedMappings,
      [`${moduleKey}[${sourceIndex}]`]: {
        targetPath: fieldMappings[moduleKey as keyof typeof fieldMappings].targetPath,
        targetIndex
      }
    };
    setSelectedMappings(newMappings);
    store.setArgusMapping(`${moduleKey}[${sourceIndex}]`, {
      targetPath: fieldMappings[moduleKey as keyof typeof fieldMappings].targetPath,
      targetIndex
    });
  };

  // 获取Argus数据中的目标数组
  const getArgusTargetArray = (targetPath: string) => {
    if (!store.argusData) return [];
    
    const paths = targetPath.split('.');
    let current = store.argusData;
    
    for (const path of paths) {
      current = current?.[path];
      if (!current) return [];
    }
    
    return Array.isArray(current) ? current : [];
  };

  // 渲染Argus目标数据
  const renderArgusTargetData = (targetPath: string, index?: number) => {
    const targetData = getArgusTargetArray(targetPath);
    if (index !== undefined && targetData[index]) {
      return (
        <div className="text-sm space-y-1">
          {Object.entries(targetData[index]).map(([key, value]) => (
            <div key={key} className="flex justify-between">
              <span className="font-medium text-gray-700">{key}:</span>
              <span className="text-gray-600">{JSON.stringify(value)}</span>
            </div>
          ))}
        </div>
      );
    }
    return <span className="text-gray-500 text-sm">(空)</span>;
  };

  // 渲染字段映射
  const renderFieldMapping = (moduleKey: string) => {
    const sourceData = getCurrentReportData(moduleKey);
    const config = fieldMappings[moduleKey as keyof typeof fieldMappings];
    
    if (Array.isArray(sourceData)) {
      const targetArray = getArgusTargetArray(config.targetPath);
      
      return (
        <div className="space-y-4">
          <h5 className="font-medium text-gray-900">数组映射</h5>
          {sourceData.map((item, sourceIndex) => {
            const mapping = selectedMappings[`${moduleKey}[${sourceIndex}]`];

            // 检查该项在diffItems中的状态
            const isNewFromDiff = store.diffItems.some(diffItem => {
              const pathInfo = parseArrayPath(diffItem.path);
              return pathInfo &&
                     pathInfo.module === moduleKey &&
                     pathInfo.index === sourceIndex &&
                     diffItem.oldValue === undefined; // oldValue为undefined表示新增
            });

            // 如果没有映射配置，或者在diffItems中标记为新增，则认为是新增项
            const isNew = !mapping || mapping.targetIndex === targetArray.length || isNewFromDiff;
            
            return (
              <div key={sourceIndex} className="border border-gray-200 rounded-lg p-4">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {/* 源数据 */}
                  <div>
                    <h6 className="font-medium text-blue-700 mb-2">源数据项 {sourceIndex + 1}</h6>
                    <div className="bg-blue-50 p-3 rounded text-sm">
                      {Object.entries(item).map(([key, value]) => (
                        <div key={key} className="flex justify-between">
                          <span className="font-medium">{key}:</span>
                          <span className="text-gray-600">{JSON.stringify(value)}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  {/* 目标数据 */}
                  <div>
                    <h6 className="font-medium text-purple-700 mb-2">Argus目标数据</h6>
                    {isNew ? (
                      <div className="bg-green-50 p-3 rounded text-sm">
                        <span className="text-green-700 font-medium">新增项</span>
                        <p className="text-gray-600 mt-1">将添加到Argus报告末尾</p>
                      </div>
                    ) : (
                      <div className="bg-purple-50 p-3 rounded">
                        <select
                          className="border border-gray-300 rounded px-2 py-1 text-sm w-full mb-2"
                          value={mapping?.targetIndex ?? ''}
                          onChange={(e) => handleArrayItemMapping(moduleKey, sourceIndex, parseInt(e.target.value))}
                        >
                          <option value="">选择目标位置</option>
                          {targetArray.map((targetItem, targetIndex) => (
                            <option key={targetIndex} value={targetIndex}>
                              目标项 {targetIndex + 1}
                            </option>
                          ))}
                        </select>
                        {mapping?.targetIndex !== undefined && (
                          <div className="text-xs">
                            {renderArgusTargetData(config.targetPath, mapping.targetIndex)}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="mt-3">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={isNew}
                      onChange={(e) => {
                        if (e.target.checked) {
                          handleArrayItemMapping(moduleKey, sourceIndex, targetArray.length);
                        } else {
                          handleArrayItemMapping(moduleKey, sourceIndex, 0);
                        }
                      }}
                      className="mr-2"
                    />
                    <span className="text-sm">标记为新增项</span>
                  </label>
                </div>
              </div>
            );
          })}
        </div>
      );
    } else {
      return (
        <div className="space-y-3">
          <h5 className="font-medium text-gray-900">字段映射</h5>
          {Object.entries(config.fields).map(([sourceField, targetField]) => {
            const value = sourceData[sourceField];
            const targetObj = getArgusTargetArray(config.targetPath);
            
            return (
              <div key={sourceField} className="border border-gray-200 rounded-lg p-3">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div>
                    <span className="font-medium text-blue-700">{sourceField}:</span>
                    <div className="bg-blue-50 p-2 rounded text-sm mt-1">
                      {JSON.stringify(value)}
                    </div>
                  </div>
                  <div>
                    <span className="font-medium text-purple-700">→ {targetField}:</span>
                    <div className="bg-purple-50 p-2 rounded text-sm mt-1">
                      {renderArgusTargetData(config.targetPath)}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      );
    }
  };

  const handleGenerateArgusReport = () => {
    store.generateArgusReport();
    store.nextStep();
  };

  if (!store.argusData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
        <span className="ml-2 text-gray-600">加载Argus数据中...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
        <h3 className="text-lg font-medium text-purple-900 mb-2">步骤 3: 应用到Argus报告</h3>
        <p className="text-purple-700">
          将当前报告数据映射到Argus报告格式。选择需要映射的字段和数组项。
        </p>
      </div>

      <div className="space-y-6">
        <div className="border border-gray-200 rounded-lg p-4">
          <h4 className="text-lg font-medium text-gray-900 mb-3">当前报告数据映射</h4>
          <div className="space-y-4">
            {Object.keys(fieldMappings).map(moduleKey => (
              <div key={moduleKey} className="border border-gray-100 rounded p-3">
                <h5 className="font-medium text-gray-700 mb-2">{moduleKey}</h5>
                {renderFieldMapping(moduleKey)}
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="flex justify-between pt-6 border-t border-gray-200">
        <button
          onClick={() => store.prevStep()}
          className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700"
        >
          上一步
        </button>
        <button
          onClick={handleGenerateArgusReport}
          className="bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700"
        >
          生成Argus报告
        </button>
      </div>
    </div>
  );
});

export default ArgusMapping;