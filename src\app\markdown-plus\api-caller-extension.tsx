import React, { useState, useRef, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { BiErrorCircle, BiCheckCircle, BiInfoCircle, BiShow } from 'react-icons/bi';
import { MarkdownViewModal } from './MarkdownViewModal';

const JsonView = dynamic(() => import('@microlink/react-json-view'), {
  ssr: false,
});

// 标准 API 响应结构
interface StandardApiResponse {
  data: any;
  code: number; // 10000 为成功
  message: string; // 错误信息
}

// API调用历史记录类型
interface ApiCallRecord {
  id: string;
  timestamp: string;
  method: string;
  url: string;
  headers: Record<string, string>;
  params: Record<string, any>;
  response: any;
  status: number;
  duration: number;
  success: boolean;
  buttonText?: string; // 添加按钮文本字段，用于表格按钮调用
}

// API参数定义类型
interface ApiParam {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'json' | 'enum' | 'file' | 'files' | 'dict' | 'list<string>';
  required: boolean;
  defaultValue?: any;
  description?: string;
  enumOptions?: string[]; // 枚举选项
  accept?: string; // 文件类型限制，如 "image/*,application/pdf"
  dictUrl?: string; // dict 类型的数据源 URL
  dictOptions?: string[]; // dict 类型加载的选项数据
}

// API接口定义类型
interface ApiDefinition {
  id: string;
  name: string;
  description: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  url: string;
  headers?: Record<string, string>;
  params: ApiParam[];
  streaming?: boolean; // 是否为流式接口
}

// API调用组件
interface ApiCallerProps {
  definition: ApiDefinition;
  className?: string;
  onSuccess?: () => void; // 成功回调函数
}

// 自适应高度的 textarea 组件
const AutoResizeTextarea: React.FC<{
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  minRows?: number;
}> = ({ value, onChange, placeholder, className, minRows = 1 }) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const adjustHeight = React.useCallback(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      // 重置高度以获取正确的 scrollHeight
      textarea.style.height = 'auto';

      // 计算最小高度（基于 minRows）
      const lineHeight = parseInt(getComputedStyle(textarea).lineHeight) || 20;
      const minHeight = lineHeight * minRows + 16; // 16px for padding

      // 设置高度为内容高度和最小高度的较大值
      const newHeight = Math.max(textarea.scrollHeight, minHeight);
      textarea.style.height = `${newHeight}px`;
    }
  }, [minRows]);

  useEffect(() => {
    adjustHeight();
  }, [value, adjustHeight]);

  useEffect(() => {
    adjustHeight();
  }, [adjustHeight]);

  return (
    <textarea
      ref={textareaRef}
      value={value}
      onChange={(e) => {
        onChange(e.target.value);
        // 延迟调整高度，确保内容已更新
        setTimeout(adjustHeight, 0);
      }}
      placeholder={placeholder}
      className={className}
      style={{
        resize: 'none',
        overflow: 'hidden',
        minHeight: `${minRows * 20 + 16}px`
      }}
      rows={minRows}
    />
  );
};

// Header 输入组件
const HeaderInput: React.FC<{
  headerName: string;
  value: string;
  error: string;
  onChange: (headerName: string, value: string) => void;
}> = ({ headerName, value, error, onChange }) => {
  return (
    <div className="space-y-1">
      <div>
        <label className="block text-xs font-medium text-gray-700">
          {headerName}
          <span className="text-xs text-gray-500 ml-1">(header)</span>
        </label>
      </div>
      <div>
        <AutoResizeTextarea
          value={value}
          onChange={(newValue) => onChange(headerName, newValue)}
          className={`w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
            error ? 'border-red-300 bg-red-50' : 'border-gray-300'
          }`}
          placeholder={`请输入 ${headerName} 的值`}
          minRows={1}
        />
      </div>
      {error && (
        <p className="text-xs text-red-600">{error}</p>
      )}
    </div>
  );
};

// 参数输入组件
const ApiParamInput: React.FC<{  
  definition: ApiDefinition;
  param: ApiParam;
  value: any;
  files: Record<string, FileList | File[]>;
  dictData: Record<string, string[]>;
  dictLoading: Record<string, boolean>;
  onParamChange: (paramName: string, value: any) => void;
  onFileChange: (paramName: string, fileList: FileList | null) => void;
}> = ({ definition, param, value, files, dictData, dictLoading, onParamChange, onFileChange }) => {
  switch (param.type) {
    case 'boolean':
      return (
        <select
          value={value?.toString() || ''}
          onChange={(e) => onParamChange(param.name, e.target.value === 'true')}
          className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">请选择</option>
          <option value="true">true</option>
          <option value="false">false</option>
        </select>
      );
    
    case 'number':
      return (
        <input
          type="number"
          value={value || ''}
          onChange={(e) => onParamChange(param.name, parseFloat(e.target.value) || '')}
          className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder={param.description || `请输入${param.name}`}
        />
      );
    
    case 'enum':
      return (
        <select
          value={value || ''}
          onChange={(e) => onParamChange(param.name, e.target.value)}
          className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">请选择</option>
          {param.enumOptions?.map((option, index) => (
            <option key={index} value={option}>
              {option}
            </option>
          ))}
        </select>
      );
    
    case 'file':
      return (
        <div>
          <input
            type="file"
            key={files[param.name]? '1': '2'}
            id={`file-input-${encodeURIComponent(definition.url)}-${param.name}`}
            accept={param.accept}
            onChange={(e) => onFileChange(param.name, e.target.files)}
            className="hidden"
          />
          <label
            htmlFor={`file-input-${encodeURIComponent(definition.url)}-${param.name}`}
            className="w-full p-3 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-blue-400 hover:bg-blue-50 transition-colors duration-200 flex flex-col items-center justify-center min-h-[80px] bg-gray-50"
          >
            <div className="flex items-center space-x-2 text-gray-600">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
              <span className="text-sm font-medium">
                {files[param.name] && files[param.name].length > 0
                  ? '点击更换文件'
                  : '点击选择文件'
                }
              </span>
            </div>
            {param.accept && (
              <div className="text-xs text-gray-500 mt-1">
                支持格式: {param.accept}
              </div>
            )}
          </label>
          {files[param.name] && files[param.name].length > 0 && (
            <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded text-sm text-green-700">
              <div className="flex items-center space-x-2">
                <svg className="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>已选择: {files[param.name][0].name} ({(files[param.name][0].size / 1024).toFixed(2)} KB)</span>
              </div>
            </div>
          )}
        </div>
      );
    
    case 'files':
      return (
        <div>
          <input
            type="file"
            key={files[param.name]? '1': '2'}
            id={`file-input-${encodeURIComponent(definition.url)}-${param.name}`}
            accept={param.accept}
            onChange={(e) => onFileChange(param.name, e.target.files)}
            className="hidden"
          />
          
          <label
            htmlFor={`file-input-${encodeURIComponent(definition.url)}-${param.name}`}
            className="w-full p-3 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-blue-400 hover:bg-blue-50 transition-colors duration-200 flex flex-col items-center justify-center min-h-[80px] bg-gray-50"
          >
            <div className="flex items-center space-x-2 text-gray-600">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
              <span className="text-sm font-medium">
                {files[param.name] && files[param.name].length > 0
                  ? '点击更换文件'
                  : '点击选择文件'
                }
              </span>
            </div>
            {param.accept && (
              <div className="text-xs text-gray-500 mt-1">
                支持格式: {param.accept}
              </div>
            )}
          </label>
          {files[param.name] && files[param.name].length > 0 && (
            <div className="mt-2 text-sm text-gray-600">
              已选择 {files[param.name].length} 个文件:
              <ul className="list-disc list-inside mt-1">
                {Array.from(files[param.name]).map((file, index) => (
                  <li key={index}>
                    {file.name} ({(file.size / 1024).toFixed(2)} KB)
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      );
    
    case 'json':
      return (
        <textarea
          value={typeof value === 'string' ? value : JSON.stringify(value, null, 2)}
          onChange={(e) => {
            try {
              const parsed = JSON.parse(e.target.value);
              onParamChange(param.name, parsed);
            } catch {
              onParamChange(param.name, e.target.value);
            }
          }}
          rows={4}
          className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-sm"
          placeholder='{"key": "value"}'
        />
      );

    case 'dict':
      const options = dictData[param.name] || [];
      const isLoading = dictLoading[param.name] || false;

      return (
        <div>
          <select
            value={value || ''}
            onChange={(e) => onParamChange(param.name, e.target.value)}
            disabled={isLoading}
            className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
          >
            <option value="">请选择</option>
            {options.map((option, index) => (
              <option key={index} value={option}>
                {option}
              </option>
            ))}
          </select>
          {isLoading && (
            <div className="mt-1 text-sm text-gray-500">加载选项中...</div>
          )}
          {!isLoading && options.length === 0 && (
            <div className="mt-1 text-sm text-red-500">无可用选项</div>
          )}
        </div>
      );

    case 'list<string>':
      return (
        <textarea
          value={value || ''}
          onChange={(e) => onParamChange(param.name, e.target.value)}
          rows={4}
          className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-y"
          placeholder="每行输入一个字符串项目&#10;例如：&#10;项目1&#10;项目2&#10;项目3"
        />
      );

    default:
      return (
        <AutoResizeTextarea
          value={value || ''}
          onChange={(newValue) => onParamChange(param.name, newValue)}
          className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder={param.description || `请输入${param.name}`}
          minRows={1}
        />
      );
  }
};

const ApiCaller: React.FC<ApiCallerProps> = ({ definition, className = '', onSuccess }) => {
  const [params, setParams] = useState<Record<string, any>>(() => {
    const initialParams: Record<string, any> = {};
    definition.params.forEach(param => {
      if (param.defaultValue !== undefined) {
        initialParams[param.name] = param.defaultValue;
      }
    });
    return initialParams;
  });

  const [loading, setLoading] = useState(false);
  const [response, setResponse] = useState<any>(null);
  const [responseType, setResponseType] = useState<'json' | 'text' | 'other'>('json');
  const [error, setError] = useState<string>('');
  const [callHistory, setCallHistory] = useState<ApiCallRecord[]>([]);
  const [showHistory, setShowHistory] = useState(false);
  const [dictData, setDictData] = useState<Record<string, string[]>>({});
  const [dictLoading, setDictLoading] = useState<Record<string, boolean>>({});
  const [expandedRecord, setExpandedRecord] = useState<string | null>(null);
  const [files, setFiles] = useState<Record<string, FileList | File[]>>({});
  const [headers, setHeaders] = useState<Record<string, string>>(() => {
    const initialHeaders: Record<string, string> = {};
    if (definition.headers) {
      Object.keys(definition.headers).forEach(key => {
        initialHeaders[key] = definition.headers![key] || '';
      });
    }
    return initialHeaders;
  });
  const [headerErrors, setHeaderErrors] = useState<Record<string, string>>({});
  const [lastCallStatus, setLastCallStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [markdownModal, setMarkdownModal] = useState<{
    isOpen: boolean;
    title: string;
    content: string;
  }>({
    isOpen: false,
    title: '',
    content: ''
  });
  const startTimeRef = useRef<number>(0);
  const resultsPanelRef = useRef<HTMLDivElement>(null);

  // 转换 list<string> 类型参数
  const convertListStringParam = (value: string): string[] => {
    if (!value || typeof value !== 'string') return [];
    return value.split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0);
  };

  // 处理参数值，根据参数类型进行转换
  const processParamValue = (param: ApiParam, value: any): any => {
    if (param.type === 'list<string>' && typeof value === 'string') {
      return convertListStringParam(value);
    }
    return value;
  };

  // 验证 header 值是否包含不允许的字符
  const validateHeaderValue = (value: string): string | null => {
    if (!value) return null;

    // HTTP header 值不允许的字符：控制字符（除了 tab）、换行符、回车符
    const invalidChars = /[\x00-\x08\x0A-\x1F\x7F]/;
    if (invalidChars.test(value)) {
      return 'Header 值包含不允许的控制字符';
    }

    // 检查是否包含换行符
    if (value.includes('\n') || value.includes('\r')) {
      return 'Header 值不能包含换行符';
    }

    return null;
  };

  // 处理 header 值变化
  const handleHeaderChange = (headerName: string, value: string) => {
    setHeaders(prev => ({
      ...prev,
      [headerName]: value
    }));

    // 验证 header 值
    const error = validateHeaderValue(value);
    setHeaderErrors(prev => ({
      ...prev,
      [headerName]: error || ''
    }));
  };

  // 检测对象中是否包含markdown字段
  const hasMarkdownFields = (obj: any): boolean => {
    if (!obj || typeof obj !== 'object') return false;

    const checkObject = (item: any): boolean => {
      if (Array.isArray(item)) {
        return item.some(checkObject);
      } else if (item && typeof item === 'object') {
        return Object.keys(item).some(key =>
          key.toLowerCase().includes('markdown') || checkObject(item[key])
        );
      }
      return false;
    };

    return checkObject(obj);
  };

  // 获取markdown字段
  const getMarkdownFields = (obj: any, prefix = ''): Array<{key: string, value: string}> => {
    const fields: Array<{key: string, value: string}> = [];

    const extractFields = (item: any, currentPrefix: string) => {
      if (Array.isArray(item)) {
        item.forEach((subItem, index) => {
          extractFields(subItem, `${currentPrefix}[${index}]`);
        });
      } else if (item && typeof item === 'object') {
        Object.entries(item).forEach(([key, value]) => {
          const fullKey = currentPrefix ? `${currentPrefix}.${key}` : key;
          if (key.toLowerCase().includes('markdown') && typeof value === 'string') {
            fields.push({ key: fullKey, value });
          } else if (typeof value === 'object') {
            extractFields(value, fullKey);
          }
        });
      }
    };

    extractFields(obj, prefix);
    return fields;
  };

  // 打开markdown查看modal
  const openMarkdownModal = (title: string, content: string) => {
    setMarkdownModal({
      isOpen: true,
      title,
      content
    });
  };

  // 关闭markdown查看modal
  const closeMarkdownModal = () => {
    setMarkdownModal({
      isOpen: false,
      title: '',
      content: ''
    });
  };

  // 加载 dict 类型参数的数据
  const loadDictData = React.useCallback(async (param: ApiParam) => {
    if (param.type !== 'dict' || !param.dictUrl) return;

    setDictLoading(prev => ({ ...prev, [param.name]: true }));

    try {
      const response = await fetch(param.dictUrl);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      // 检查是否为标准 API 响应结构
      let data: string[] = [];
      if (result.code === 10000 && Array.isArray(result.data)) {
        data = result.data;
      } else if (Array.isArray(result)) {
        data = result;
      } else {
        throw new Error('Invalid response format');
      }

      setDictData(prev => ({ ...prev, [param.name]: data }));

      // 如果参数没有默认值且数据不为空，设置第一个选项为默认值
      if (!params[param.name] && data.length > 0) {
        handleParamChange(param.name, data[0]);
      }
    } catch (error) {
      console.error(`Failed to load dict data for ${param.name}:`, error);
      setDictData(prev => ({ ...prev, [param.name]: [] }));
    } finally {
      setDictLoading(prev => ({ ...prev, [param.name]: false }));
    }
  }, [params]);

  // 初始化加载所有 dict 类型参数的数据
  React.useEffect(() => {
    definition.params.forEach(param => {
      if (param.type === 'dict') {
        loadDictData(param);
      }
    });
  }, [definition.params, loadDictData]);

  // 处理参数值变化
  const handleParamChange = (paramName: string, value: any) => {
    setParams(prev => ({
      ...prev,
      [paramName]: value
    }));
  };

  // 处理文件变化
  const handleFileChange = (paramName: string, fileList: FileList | null) => {
    if (fileList) {
      setFiles(prev => ({
        ...prev,
        [paramName]: fileList
      }));
    } else {
      setFiles(prev => {
        const newFiles = { ...prev };
        delete newFiles[paramName];
        return newFiles;
      });
    }
  };

  // 验证参数
  const validateParams = (): string | null => {
    for (const param of definition.params) {
      if (param.required) {
        if (param.type === 'file' || param.type === 'files') {
          const fileList = files[param.name];
          if (!fileList || fileList.length === 0) {
            return `文件 "${param.name}" 是必填项`;
          }
        } else if (params[param.name] === undefined || params[param.name] === '') {
          return `参数 "${param.name}" 是必填项`;
        }
      }
    }
    return null;
  };

  // 流式API调用
  const callStreamingApi = async () => {
    const validationError = validateParams();
    if (validationError) {
      setError(validationError);
      setLastCallStatus('error');
      return;
    }

    setLoading(true);
    setError('');
    setResponse('');
    setResponseType('json');
    setLastCallStatus('idle');
    startTimeRef.current = Date.now();

    // 滚动到结果面板顶部
    if (resultsPanelRef.current) {
      resultsPanelRef.current.scrollTop = 0;
    }

    try {
      let url = definition.url;
      let body: any = undefined;
      const requestHeaders: Record<string, string> = { ...definition.headers };

      // 合并用户输入的 headers（只添加有值且无错误的 headers）
      if (definition.headers) {
        Object.keys(definition.headers).forEach(key => {
          const userValue = headers[key];
          const hasError = headerErrors[key];
          if (userValue && !hasError) {
            requestHeaders[key] = userValue;
          }
        });
      }

      // 检查是否有文件参数
      const hasFiles = definition.params.some(param =>
        (param.type === 'file' || param.type === 'files') && files[param.name]
      );

      // 处理参数
      if (definition.method === 'GET') {
        // GET请求：参数放在URL中（文件不支持GET）
        const searchParams = new URLSearchParams();
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            const param = definition.params.find(p => p.name === key);
            const processedValue = param ? processParamValue(param, value) : value;
            // 对于 list<string> 类型，在 GET 请求中将数组转换为逗号分隔的字符串
            const stringValue = Array.isArray(processedValue) ? processedValue.join(',') : String(processedValue);
            searchParams.append(key, stringValue);
          }
        });
        const queryString = searchParams.toString();
        if (queryString) {
          url += (url.includes('?') ? '&' : '?') + queryString;
        }
      } else {
        if (hasFiles) {
          // 有文件上传时使用 FormData
          const formData = new FormData();

          // 添加普通参数
          Object.entries(params).forEach(([key, value]) => {
            if (value !== undefined && value !== '') {
              const param = definition.params.find(p => p.name === key);
              const processedValue = param ? processParamValue(param, value) : value;
              // 对于 FormData，list<string> 类型需要特殊处理
              if (Array.isArray(processedValue)) {
                processedValue.forEach(item => formData.append(key, String(item)));
              } else {
                formData.append(key, String(processedValue));
              }
            }
          });

          // 添加文件
          Object.entries(files).forEach(([key, fileList]) => {
            const param = definition.params.find(p => p.name === key);
            if (param?.type === 'files') {
              // 多文件上传
              Array.from(fileList).forEach(file => {
                formData.append(key, file);
              });
            } else if (param?.type === 'file' && fileList.length > 0) {
              // 单文件上传
              formData.append(key, fileList[0]);
            }
          });

          body = formData;
          // 不设置 Content-Type，让浏览器自动设置 multipart/form-data
        } else {
          // 无文件时使用 JSON
          requestHeaders['Content-Type'] = 'application/json';
          // 处理参数类型转换
          const processedParams: Record<string, any> = {};
          Object.entries(params).forEach(([key, value]) => {
            const param = definition.params.find(p => p.name === key);
            processedParams[key] = param ? processParamValue(param, value) : value;
          });
          body = JSON.stringify(processedParams);
        }
      }

      const fetchOptions: RequestInit = {
        method: definition.method,
        headers: requestHeaders,
      };

      if (body) {
        fetchOptions.body = body;
      }

      const response = await fetch(url, fetchOptions);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      if (!response.body) {
        throw new Error('响应体为空');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let streamedContent = '';
      let bufferedData = ''; 

      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            // 如果返回结果为空，则直接抛出异常，阻断流程
           
            break;
          }

          bufferedData += decoder.decode(value, { stream: true });

          // 处理按行分割的数据
          let boundary = bufferedData.indexOf('\n');
          while (boundary !== -1) {
            const chunk = bufferedData.slice(0, boundary).trim();
            bufferedData = bufferedData.slice(boundary + 1);

            if (chunk.startsWith('data: ')) { 
              const jsonString = chunk.slice(6).trim();
              if (jsonString) {
                const data = JSON.parse(jsonString);

                // 处理不同类型的数据
                switch (data.type) {
                  case 'error':
                    throw new Error(data.message || '服务器返回错误');

                  case 'heartbeat':
                    // 心跳数据，不需要显示，只用于保持连接
                    console.log('收到心跳:', data);
                    break;

                  case 'message':
                    // 实际数据内容
                    if (data.message) {
                      streamedContent = data.message;
                      setResponse(streamedContent);
                    } 
                    break;

                  case 'end':
                    // 流结束标记
                    console.log('流式处理结束:', data);
                    // if (data.final_result) {
                    //   resultData = { ...resultData, ...data.final_result };
                    // }
                    break;

                  default:
                    // 其他类型的数据，直接添加到内容中
                    // const displayContent = data.content || JSON.stringify(data);
                    // streamedContent += displayContent + '\n';
                    // setResponse(streamedContent);
                    break;
                }
              }
               
            } else if (chunk.trim()) {
              // 不是以"data: "开头的数据，直接作为文本内容
              streamedContent += chunk + '\n';
              setResponse(streamedContent);
            }

            boundary = bufferedData.indexOf('\n');
          }
        }

        const duration = Date.now() - startTimeRef.current;

        // 创建调用记录
        const record: ApiCallRecord = {
          id: `call_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          timestamp: new Date().toISOString(),
          method: definition.method,
          url,
          headers: requestHeaders,
          params,
          response: streamedContent,
          status: response.status,
          duration,
          success: true
        };

        setCallHistory(prev => [record, ...prev]);
        setLastCallStatus('success');

        // API调用成功，触发成功回调
        if (onSuccess) {
          onSuccess();
        }
      } finally {
        reader.releaseLock();
      }
    } catch (err) {
      const duration = Date.now() - startTimeRef.current;
      const errorMessage = err instanceof Error ? err.message : '未知错误';

      // 构建错误记录的 headers
      const errorHeaders: Record<string, string> = { ...definition.headers };
      if (definition.headers) {
        Object.keys(definition.headers).forEach(key => {
          const userValue = headers[key];
          const hasError = headerErrors[key];
          if (userValue && !hasError) {
            errorHeaders[key] = userValue;
          }
        });
      }

      // 创建错误记录
      const record: ApiCallRecord = {
        id: `call_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        timestamp: new Date().toISOString(),
        method: definition.method,
        url: definition.url,
        headers: errorHeaders,
        params,
        response: { error: errorMessage },
        status: 0,
        duration,
        success: false
      };

      setCallHistory(prev => [record, ...prev]);
      setError(errorMessage);
      setLastCallStatus('error');
    } finally {
      setFiles({});
      setLoading(false);
    }
  };

  // 调用API
  const callApi = async () => {
    // 如果是流式接口，使用流式处理
    if (definition.streaming) {
      return callStreamingApi();
    }

    const validationError = validateParams();
    if (validationError) {
      setError(validationError);
      setLastCallStatus('error');
      return;
    }

    setLoading(true);
    setError('');
    setResponse(null);
    setResponseType('json');
    setLastCallStatus('idle');
    startTimeRef.current = Date.now();

    // 滚动到结果面板顶部
    if (resultsPanelRef.current) {
      resultsPanelRef.current.scrollTop = 0;
    }

    try {
      let url = definition.url;
      let body: any = undefined;
      const requestHeaders: Record<string, string> = { ...definition.headers };

      // 合并用户输入的 headers（只添加有值且无错误的 headers）
      if (definition.headers) {
        Object.keys(definition.headers).forEach(key => {
          const userValue = headers[key];
          const hasError = headerErrors[key];
          if (userValue && !hasError) {
            requestHeaders[key] = userValue;
          }
        });
      }

      // 检查是否有文件参数
      const hasFiles = definition.params.some(param =>
        (param.type === 'file' || param.type === 'files') && files[param.name]
      );

      // 处理参数
      if (definition.method === 'GET') {
        // GET请求：参数放在URL中（文件不支持GET）
        const searchParams = new URLSearchParams();
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            const param = definition.params.find(p => p.name === key);
            const processedValue = param ? processParamValue(param, value) : value;
            // 对于 list<string> 类型，在 GET 请求中将数组转换为逗号分隔的字符串
            const stringValue = Array.isArray(processedValue) ? processedValue.join(',') : String(processedValue);
            searchParams.append(key, stringValue);
          }
        });
        const queryString = searchParams.toString();
        if (queryString) {
          url += (url.includes('?') ? '&' : '?') + queryString;
        }
      } else {
        if (hasFiles) {
          // 有文件上传时使用 FormData
          const formData = new FormData();
          
          // 添加普通参数
          Object.entries(params).forEach(([key, value]) => {
            if (value !== undefined && value !== '') {
              const param = definition.params.find(p => p.name === key);
              const processedValue = param ? processParamValue(param, value) : value;
              // 对于 FormData，list<string> 类型需要特殊处理
              if (Array.isArray(processedValue)) {
                processedValue.forEach(item => formData.append(key, String(item)));
              } else {
                formData.append(key, String(processedValue));
              }
            }
          });

          // 添加文件
          Object.entries(files).forEach(([key, fileList]) => {
            const param = definition.params.find(p => p.name === key);
            if (param?.type === 'files') {
              // 多文件上传
              Array.from(fileList).forEach(file => {
                formData.append(key, file);
              });
            } else if (param?.type === 'file' && fileList.length > 0) {
              // 单文件上传
              formData.append(key, fileList[0]);
            }
          });

          body = formData;
          // 不设置 Content-Type，让浏览器自动设置 multipart/form-data
        } else {
          // 无文件时使用 JSON
          requestHeaders['Content-Type'] = 'application/json';
          // 处理参数类型转换
          const processedParams: Record<string, any> = {};
          Object.entries(params).forEach(([key, value]) => {
            const param = definition.params.find(p => p.name === key);
            processedParams[key] = param ? processParamValue(param, value) : value;
          });
          body = JSON.stringify(processedParams);
        }
      }

      const fetchOptions: RequestInit = {
        method: definition.method,
        headers: requestHeaders,
      };

      if (body) {
        fetchOptions.body = body;
      }

      const response = await fetch(url, fetchOptions);
      const duration = Date.now() - startTimeRef.current;
      
      let responseData;
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        responseData = await response.json();
        setResponseType('json');
      } else {
        responseData = await response.text();
        setResponseType('text');
      }

      // 检查是否为标准 API 响应结构
      let actualData = responseData;
      let isSuccess = response.ok;
      let errorMessage = '';

      if (typeof responseData === 'object' && responseData !== null && 'code' in responseData) {
        // 标准 API 响应结构
        const standardResponse = responseData as StandardApiResponse;
        isSuccess = standardResponse.code === 10000;
        actualData = isSuccess ? standardResponse.data : responseData;
        if (!isSuccess) {
          errorMessage = standardResponse.message || '请求失败';
        }
      } else if (!response.ok) {
        // 非标准结构，使用 HTTP 状态判断
        errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      }

      // 创建调用记录
      const record: ApiCallRecord = {
        id: `call_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        timestamp: new Date().toISOString(),
        method: definition.method,
        url,
        headers: requestHeaders,
        params,
        response: responseData, // 保存原始响应用于历史记录
        status: response.status,
        duration,
        success: isSuccess
      };

      setCallHistory(prev => [record, ...prev]);
      setResponse(actualData); // 显示实际数据

      if (!isSuccess) {
        setError(errorMessage);
        setLastCallStatus('error');
      } else {
        setLastCallStatus('success');
        // API调用成功，触发成功回调
        if (onSuccess) {
          onSuccess();
        }
      }
    } catch (err) {
      const duration = Date.now() - startTimeRef.current;
      const errorMessage = err instanceof Error ? err.message : '未知错误';

      // 构建错误记录的 headers
      const errorHeaders: Record<string, string> = { ...definition.headers };
      if (definition.headers) {
        Object.keys(definition.headers).forEach(key => {
          const userValue = headers[key];
          const hasError = headerErrors[key];
          if (userValue && !hasError) {
            errorHeaders[key] = userValue;
          }
        });
      }

      // 创建错误记录
      const record: ApiCallRecord = {
        id: `call_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        timestamp: new Date().toISOString(),
        method: definition.method,
        url: definition.url,
        headers: errorHeaders,
        params,
        response: { error: errorMessage },
        status: 0,
        duration,
        success: false
      };

      setCallHistory(prev => [record, ...prev]);
      setError(errorMessage);
      setLastCallStatus('error');
    } finally {
      setLoading(false);
    }
  };

  // 导出调用历史
  const exportHistory = () => {
    const dataStr = JSON.stringify(callHistory, null, 2);
    const blob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `api_call_history_${definition.id}_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // 清空历史记录
  const clearHistory = () => {
    setCallHistory([]);
  };

  // 状态指示器组件
  const StatusIndicator = () => {
    if (loading) {
      return (
        <div className="flex items-center p-3 bg-blue-50 border border-blue-200 rounded-lg mb-4">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500 mr-3"></div>
          <div>
            <p className="text-blue-800 font-medium">
              {definition.streaming ? '流式调用中...' : '调用中...'}
            </p>
            <p className="text-blue-600 text-sm">
              {definition.streaming ? '正在接收流式数据' : '正在处理您的请求'}
            </p>
          </div>
        </div>
      );
    }

    if (lastCallStatus === 'success') {
      return (
        <div className="flex items-center p-3 bg-green-50 border border-green-200 rounded-lg mb-4">
          <BiCheckCircle className="text-green-500 text-xl mr-3" />
          <div>
            <p className="text-green-800 font-medium">调用成功</p>
            <p className="text-green-600 text-sm">API请求已成功完成</p>
          </div>
        </div>
      );
    }
    if (lastCallStatus === 'error') {
      return null;
    }

    // if (lastCallStatus === 'error') {
    //   return (
    //     <div className="flex items-center p-3 bg-red-50 border border-red-200 rounded-lg mb-4">
    //       <BiErrorCircle className="text-red-500 text-xl mr-3" />
    //       <div>
    //         <p className="text-red-800 font-medium">调用失败</p>
    //         <p className="text-red-600 text-sm">请查看下方错误详情</p>
    //       </div>
    //     </div>
    //   );
    // }

    return (
      <div className="flex items-center p-3 bg-gray-50 border border-gray-200 rounded-lg mb-4">
        <BiInfoCircle className="text-gray-500 text-xl mr-3" />
        <div>
          <p className="text-gray-700 font-medium">等待调用</p>
          <p className="text-gray-600 text-sm">设置参数后点击调用按钮</p>
        </div>
      </div>
    );
  };

  return (
    <div className={`api-caller bg-white rounded-lg border border-gray-200 shadow-sm ${className}`}>
      {/* 左右分栏布局 */}
      <div className="flex flex-col md:flex-row h-[600px]">
        {/* 左侧：接口信息和参数设置 */}
        <div className="md:w-1/2 border-r border-gray-200 flex flex-col">
          {/* 接口信息 */}
          <div className="p-2 border-b border-gray-200 bg-gray-50">
            <div>
              <h3 className="text-sm font-semibold text-gray-900 m-0.5!">{definition.name}</h3>
              <p className="text-xs text-gray-600">{definition.description}</p>
              <div className="flex items-center mt-1 space-x-2">
                <span className={`px-1.5 py-0.5 text-xs font-medium rounded ${
                  definition.method === 'GET' ? 'bg-green-100 text-green-800' :
                  definition.method === 'POST' ? 'bg-blue-100 text-blue-800' :
                  definition.method === 'PUT' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-red-100 text-red-800'
                }`}>
                  {definition.method}
                </span>
                <code className="text-xs text-gray-700 bg-gray-100 px-1.5 py-0.5 rounded">{definition.url}</code>
              </div>
            </div>
          </div>

          {/* 参数输入 */}
          {definition.params.length > 0 ? (
            <div className="flex-1 p-3 border-b border-gray-200 overflow-y-auto">
              <h4 className="text-sm font-medium text-gray-900 mb-2">参数设置</h4>
              <div className="space-y-3">
                {definition.params.map((param) => (
                  <div key={param.name} className="space-y-1">
                    <div>
                      <label className="block text-xs font-medium text-gray-700">
                        {param.name}
                        {param.required && <span className="text-red-500 ml-1">*</span>}
                        <span className="text-xs text-gray-500 ml-1">({param.type})</span>
                      </label>
                    </div>
                    <div>
                      <ApiParamInput
                        definition={definition}
                        param={param}
                        value={params[param.name]}
                        files={files}
                        dictData={dictData}
                        dictLoading={dictLoading}
                        onParamChange={handleParamChange}
                        onFileChange={handleFileChange}
                      />
                    </div>
                    {param.description && (
                      <p className="text-xs text-gray-500">{param.description}</p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="flex-1 p-3 border-b border-gray-200 flex items-center justify-center">
              <p className="text-gray-500 text-sm">此接口无需参数</p>
            </div>
          )}

          {/* Header 设置 */}
          {definition.headers && Object.keys(definition.headers).length > 0 && (
            <div className="p-3 border-b border-gray-200">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Header 设置</h4>
              <div className="space-y-3">
                {Object.keys(definition.headers).map((headerName) => (
                  <HeaderInput
                    key={headerName}
                    headerName={headerName}
                    value={headers[headerName] || ''}
                    error={headerErrors[headerName] || ''}
                    onChange={handleHeaderChange}
                  />
                ))}
              </div>
            </div>
          )}

          {/* 调用按钮 */}
          <div className="p-3 border-t border-gray-200 bg-gray-50">
            <button
              onClick={callApi}
              disabled={loading}
              className={`w-full py-2 px-3 rounded-lg text-sm font-medium transition-all duration-200 ${
                loading
                  ? 'bg-gray-400 text-white cursor-not-allowed'
                  : lastCallStatus === 'success'
                  ? 'bg-green-500 hover:bg-green-600 text-white border-2 border-green-300'
                  : lastCallStatus === 'error'
                  ? 'bg-red-500 hover:bg-red-600 text-white border-2 border-red-300'
                  : 'bg-blue-500 hover:bg-blue-600 text-white border-2 border-blue-300'
              }`}
            >
              {loading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                  调用中...
                </div>
              ) : (
                <div className="flex items-center justify-center">
                  {lastCallStatus === 'success' && <BiCheckCircle className="mr-2 text-sm" />}
                  {lastCallStatus === 'error' && <BiErrorCircle className="mr-2 text-sm" />}
                  {definition.name}
                </div>
              )}
            </button>
          </div>
        </div>

        {/* 右侧：调用结果和历史 */}
        <div className="md:w-1/2 flex flex-col h-full">
          <div
            ref={resultsPanelRef}
            className="flex-1 overflow-y-auto p-4 space-y-4"
          >
            {/* 状态指示器 */}
            <StatusIndicator />

            {/* 错误信息 */}
            {error && (
              <div className={`border-l-4 p-4 rounded-r-lg ${
                lastCallStatus === 'error' ? 'bg-red-50 border-red-400' : 'bg-red-50 border-red-200'
              }`}>
                <div className="flex items-start">
                  <BiErrorCircle className="text-red-500 text-lg mr-3 mt-0.5" />
                  <div className="flex-1">
                    <p className="text-red-800 font-medium mb-1">错误详情</p>
                    <p className="text-red-700 text-sm whitespace-pre-wrap">{error}</p>
                  </div>
                </div>
              </div>
            )}

            {/* 响应结果 */}
            {response && (
              <div className={`border-l-4 rounded-r-lg ${
                lastCallStatus === 'success'
                  ? 'bg-green-50 border-green-400'
                  : 'bg-gray-50 border-gray-300'
              }`}>
                <div className="p-4">
                  <div className="flex items-center mb-3">
                    {lastCallStatus === 'success' && <BiCheckCircle className="text-green-500 text-lg mr-2" />}
                    <h4 className="text-md font-medium text-gray-900">响应结果</h4>
                  </div>
                  <div className="bg-white rounded-lg p-3 max-h-80 overflow-auto border">{/* 响应内容将在这里显示 */}
            {(() => {
              // 根据响应类型和数据类型选择合适的显示方式
              if (responseType === 'text' || typeof response === 'string') {
                // 文本响应或字符串类型，直接显示
                return (
                  <div>
                    <div className="mb-2 text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded inline-block">
                      {definition.streaming ? '流式响应' : '文本响应'}
                    </div>
                    <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                      {response}
                    </pre>
                    {definition.streaming && loading && (
                      <div className="mt-2 text-xs text-blue-600 flex items-center">
                        <div className="animate-pulse w-2 h-2 bg-blue-600 rounded-full mr-2"></div>
                        正在接收数据...
                      </div>
                    )}
                  </div>
                );
              } else if (typeof response === 'number' || typeof response === 'boolean') {
                // 数字或布尔值，转换为字符串显示
                return (
                  <div>
                    <div className="mb-2 text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded inline-block">
                      {typeof response} 类型
                    </div>
                    <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                      {String(response)}
                    </pre>
                  </div>
                );
              } else if (response === null) {
                // null 值
                return (
                  <div>
                    <div className="mb-2 text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded inline-block">
                      null 值
                    </div>
                    <pre className="text-sm text-gray-500 whitespace-pre-wrap">
                      null
                    </pre>
                  </div>
                );
              } else if (typeof response === 'object' && responseType === 'json') {
                // JSON 响应，检查是否有markdown字段
                const markdownFields = getMarkdownFields(response);

                try {
                  return (
                    <div>
                      <div className="mb-2 text-xs bg-green-100 text-green-700 px-2 py-1 rounded inline-block">
                        JSON 响应
                      </div>

                      {/* 显示markdown字段的查看按钮 */}
                      {markdownFields.length > 0 && (
                        <div className="mb-3 p-3 bg-blue-50 border border-blue-200 rounded">
                          <div className="text-sm font-medium text-blue-800 mb-2">
                            检测到 Markdown 字段:
                          </div>
                          <div className="space-y-2">
                            {markdownFields.map((field, index) => (
                              <div key={index} className="flex items-center justify-between">
                                <span className="text-sm text-blue-700 font-mono">
                                  {field.key}
                                </span>
                                <button
                                  onClick={() => openMarkdownModal(field.key, field.value)}
                                  className="flex items-center px-3 py-1 text-sm bg-blue-500 hover:bg-blue-600 text-white rounded transition-colors"
                                >
                                  <BiShow className="mr-1" />
                                  查看
                                </button>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      <JsonView
                        src={response}
                        theme={{
                          base00: '#f8fafc', // 背景色 - 浅灰
                          base01: '#f1f5f9', // 折叠/展开按钮背景
                          base02: '#e2e8f0', // 边框色
                          base03: '#94a3b8', // 注释/标点符号
                          base04: '#64748b', // 普通文本
                          base05: '#475569', // 键名
                          base06: '#334155', // 高亮键名
                          base07: '#1e293b', // 最亮的文本
                          base08: '#dc2626', // 错误信息
                          base09: '#ea580c', // 数字
                          base0A: '#d97706', // 布尔值
                          base0B: '#16a34a', // 字符串
                          base0C: '#0891b2', // 日期
                          base0D: '#2563eb', // URL
                          base0E: '#7c3aed', // 正则表达式
                          base0F: '#c2410c'  // 其他值
                        }}
                        name={null}
                        collapsed={2}
                        displayObjectSize={true}
                        displayDataTypes={false}
                        enableClipboard={true}
                        quotesOnKeys={false}
                        sortKeys={true}
                        style={{
                          padding: '1rem',
                          borderRadius: '0.375rem',
                          fontFamily: 'monospace',
                          backgroundColor: '#f8fafc'
                        }}
                      />
                    </div>
                  );
                } catch (error) {
                  // JsonView 渲染失败，回退到 JSON.stringify
                  return (
                    <div>
                      <div className="mb-2 text-xs text-red-500 bg-red-100 px-2 py-1 rounded inline-block">
                        JSON 解析失败，显示原始数据
                      </div>
                      <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                        {JSON.stringify(response, null, 2)}
                      </pre>
                    </div>
                  );
                }
              } else if (typeof response === 'object') {
                // 对象类型但不是 JSON 响应，使用 JSON.stringify
                return (
                  <div>
                    <div className="mb-2 text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded inline-block">
                      对象类型
                    </div>
                    <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                      {JSON.stringify(response, null, 2)}
                    </pre>
                  </div>
                );
              } else {
                // 其他类型，转换为字符串显示
                return (
                  <div>
                    <div className="mb-2 text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded inline-block">
                      未知类型
                    </div>
                    <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                      {String(response)}
                    </pre>
                  </div>
                );
              }
                    })()}
                  </div>
                </div>
              </div>
            )}

            {/* 调用历史 */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-md font-medium text-gray-900">调用历史</h4>
                <div className="space-x-2">
                  <button
                    onClick={() => setShowHistory(!showHistory)}
                    className="px-3 py-1 text-sm bg-gray-200 hover:bg-gray-300 text-gray-700 rounded transition-colors"
                  >
                    {showHistory ? '隐藏' : `显示 (${callHistory.length})`}
                  </button>
                  <button
                    onClick={exportHistory}
                    disabled={callHistory.length === 0}
                    className="px-3 py-1 text-sm bg-green-500 hover:bg-green-600 disabled:bg-gray-300 text-white rounded transition-colors"
                  >
                    导出
                  </button>
                  <button
                    onClick={clearHistory}
                    disabled={callHistory.length === 0}
                    className="px-3 py-1 text-sm bg-red-500 hover:bg-red-600 disabled:bg-gray-300 text-white rounded transition-colors"
                  >
                    清空
                  </button>
                </div>
              </div>

              {showHistory && (
                callHistory.length === 0 ? (
                  <p className="text-gray-500 text-center py-8">暂无调用记录</p>
                ) : (
                  <div className="space-y-2 max-h-64 overflow-y-auto">
              {callHistory.map((record) => (
                <div key={record.id} className="border border-gray-200 rounded">
                  <div
                    className="p-3 cursor-pointer hover:bg-gray-50 flex items-center justify-between"
                    onClick={() => setExpandedRecord(expandedRecord === record.id ? null : record.id)}
                  >
                    <div className="flex items-center space-x-3">
                      <span className={`w-2 h-2 rounded-full ${record.success ? 'bg-green-500' : 'bg-red-500'}`}></span>
                      <span className="text-sm font-medium">{record.method}</span>
                      <span className="text-sm text-gray-600">{new Date(record.timestamp).toLocaleString()}</span>
                      <span className="text-sm text-gray-500">{record.duration}ms</span>
                      <span className={`text-sm ${record.success ? 'text-green-600' : 'text-red-600'}`}>
                        {record.status || 'ERROR'}
                      </span>
                    </div>
                    <span className="text-xs text-gray-400">
                      {expandedRecord === record.id ? '收起' : '展开'}
                    </span>
                  </div>
                  
                  {expandedRecord === record.id && (
                    <div className="px-3 pb-3 border-t border-gray-200 bg-gray-50">
                      <div className="mt-2 space-y-2 text-sm">
                        <div>
                          <strong>URL:</strong> <code className="bg-gray-100 px-1 rounded">{record.url}</code>
                        </div>
                        <div>
                          <strong>参数:</strong>
                          <pre className="mt-1 text-xs bg-white p-2 rounded border overflow-auto">
                            {JSON.stringify(record.params, null, 2)}
                          </pre>
                        </div>
                        <div>
                          <strong>响应:</strong>
                          <pre className="mt-1 text-xs bg-white p-2 rounded border max-h-32 overflow-auto">
                            {JSON.stringify(record.response, null, 2)}
                          </pre>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
                  </div>
                )
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Markdown查看Modal */}
      <MarkdownViewModal
        isOpen={markdownModal.isOpen}
        onClose={closeMarkdownModal}
        title={markdownModal.title}
        content={markdownModal.content}
      />
    </div>
  );
};

export default ApiCaller;

// 导出类型定义供其他组件使用
export type { ApiDefinition, ApiParam, ApiCallRecord };